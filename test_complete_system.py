#!/usr/bin/env python3
"""
Comprehensive test for the complete PKI Document Signer System
"""

import os
import subprocess
import sys


def run_test(test_file, description):
    """Run a test file and return the result"""
    print(f"\n{'='*50}")
    print(f"Running: {description}")
    print(f"{'='*50}")
    
    try:
        result = subprocess.run([sys.executable, test_file], 
                              capture_output=True, 
                              text=True, 
                              timeout=30)
        
        if result.returncode == 0:
            print(f"✅ {description} - PASSED")
            return True
        else:
            print(f"❌ {description} - FAILED")
            print("STDOUT:", result.stdout)
            print("STDERR:", result.stderr)
            return False
            
    except subprocess.TimeoutExpired:
        print(f"⏰ {description} - TIMEOUT")
        return False
    except Exception as e:
        print(f"💥 {description} - ERROR: {str(e)}")
        return False


def check_file_structure():
    """Check that all required files exist"""
    print("\n" + "="*50)
    print("Checking File Structure")
    print("="*50)
    
    required_files = [
        # Core applications
        'pki_registration.py',
        'pki_login.py', 
        'pki_document_signer.py',
        'pki_document_verifier.py',
        'demo_pki_system.py',
        'verify_signed_document.py',
        'legal_use_case.py',
        
        # Test files
        'test_pki_registration.py',
        'test_pki_login.py',
        'test_pki_document_signer.py',
        'test_pki_document_verifier.py',
        'test_complete_workflow.py',
        'test_gui_verification.py',
        'test_legal_use_case.py',
        
        # Documentation
        'README.md',
        'QUICK_START.md',
        'SYSTEM_OVERVIEW.md',
        'sample_document.txt'
    ]
    
    missing_files = []
    for file in required_files:
        if os.path.exists(file):
            print(f"✅ {file}")
        else:
            print(f"❌ {file} - MISSING")
            missing_files.append(file)
    
    if missing_files:
        print(f"\n❌ Missing {len(missing_files)} required files")
        return False
    else:
        print(f"\n✅ All {len(required_files)} required files present")
        return True


def check_directories():
    """Check directory structure"""
    print("\nChecking Directory Structure:")
    
    # Check if user data exists
    if os.path.exists('keys') and os.path.exists('certs'):
        key_files = [f for f in os.listdir('keys') if f.endswith('_private.pem')]
        cert_files = [f for f in os.listdir('certs') if f.endswith('_cert.pem')]
        print(f"✅ User data: {len(key_files)} users registered")
    else:
        print("⚠️  No user data found (run registration first)")
    
    # Check if signed documents exist
    if os.path.exists('signed_docs'):
        signed_files = [f for f in os.listdir('signed_docs') if not f.endswith('.sig') and not f.endswith('_cert.pem')]
        print(f"✅ Signed documents: {len(signed_files)} documents")
    else:
        print("⚠️  No signed documents found (run document signer first)")
    
    return True


def main():
    """Run comprehensive system tests"""
    print("PKI Document Signer System - Comprehensive Testing")
    print("="*60)
    
    # Check file structure first
    if not check_file_structure():
        print("\n❌ File structure check failed. Cannot proceed with tests.")
        return 1
    
    check_directories()
    
    # Define all tests to run
    tests = [
        ('test_pki_registration.py', 'PKI Registration Tests'),
        ('test_pki_login.py', 'PKI Login Tests'),
        ('test_pki_document_signer.py', 'PKI Document Signer Tests'),
        ('test_pki_document_verifier.py', 'PKI Document Verifier Tests'),
        ('test_legal_use_case.py', 'Legal Use Case Tests'),
        ('test_complete_workflow.py', 'Complete Workflow Tests'),
        ('test_gui_verification.py', 'GUI Verification Tests'),
    ]
    
    # Run all tests
    results = []
    for test_file, description in tests:
        if os.path.exists(test_file):
            result = run_test(test_file, description)
            results.append((description, result))
        else:
            print(f"⚠️  Skipping {description} - test file not found")
            results.append((description, False))
    
    # Summary
    print("\n" + "="*60)
    print("TEST SUMMARY")
    print("="*60)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for description, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{description:<40} {status}")
    
    print(f"\nOverall Result: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 ALL TESTS PASSED! 🎉")
        print("\nThe PKI Document Signer System is fully functional:")
        print("  ✅ User registration and key generation")
        print("  ✅ PKI-based authentication")
        print("  ✅ Document signing with digital signatures")
        print("  ✅ Document verification (GUI and CLI)")
        print("  ✅ Legal use case documentation")
        print("  ✅ Complete workflow integration")
        print("  ✅ Comprehensive testing suite")
        
        print("\n🚀 System Ready for Use!")
        print("  • Run 'python demo_pki_system.py' to get started")
        print("  • Check README.md for detailed documentation")
        print("  • Use QUICK_START.md for step-by-step guide")
        
        return 0
    else:
        print(f"\n❌ {total - passed} tests failed. Please check the errors above.")
        return 1


if __name__ == "__main__":
    exit(main())
