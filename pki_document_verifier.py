import tkinter as tk
from tkinter import filedialog, messagebox
import os
import hashlib
import threading
import time
from cryptography.hazmat.primitives import serialization, hashes
from cryptography.hazmat.primitives.asymmetric import padding
from cryptography import x509


class PKIDocumentVerifierApp:
    def __init__(self, root):
        self.root = root
        self.document_path = None
        self.signature_path = None
        self.certificate_path = None
        self.setup_ui()
        
    def setup_ui(self):
        """Setup the user interface"""
        self.root.title("PKI Document Signer - Verify Document")
        self.root.geometry("600x400")
        self.root.resizable(False, False)
        
        # Center the window
        self.center_window()
        
        # Main frame
        main_frame = tk.Frame(self.root, padx=30, pady=20)
        main_frame.pack(expand=True, fill='both')
        
        # Title label
        title_label = tk.Label(main_frame, 
                              text="PKI Document Signer - Verify Document", 
                              font=('Arial', 16, 'bold'), 
                              fg='#2c3e50')
        title_label.pack(pady=(0, 25))
        
        # Document file selection
        doc_frame = tk.Frame(main_frame)
        doc_frame.pack(fill='x', pady=(0, 15))
        
        tk.Label(doc_frame, text="Original Document:", 
                font=('Arial', 10, 'bold')).pack(anchor='w')
        
        doc_button_frame = tk.Frame(doc_frame)
        doc_button_frame.pack(fill='x', pady=(5, 0))
        
        self.doc_button = tk.Button(doc_button_frame, 
                                   text="Select Document File",
                                   command=self.select_document,
                                   bg='#3498db', fg='white',
                                   font=('Arial', 10),
                                   padx=10, pady=5)
        self.doc_button.pack(side='left')
        
        self.doc_label = tk.Label(doc_button_frame, 
                                 text="No file selected",
                                 font=('Arial', 9), fg='#7f8c8d')
        self.doc_label.pack(side='left', padx=(10, 0))
        
        # Signature file selection
        sig_frame = tk.Frame(main_frame)
        sig_frame.pack(fill='x', pady=(0, 15))
        
        tk.Label(sig_frame, text="Signature File:", 
                font=('Arial', 10, 'bold')).pack(anchor='w')
        
        sig_button_frame = tk.Frame(sig_frame)
        sig_button_frame.pack(fill='x', pady=(5, 0))
        
        self.sig_button = tk.Button(sig_button_frame, 
                                   text="Select Signature (.sig)",
                                   command=self.select_signature,
                                   bg='#e74c3c', fg='white',
                                   font=('Arial', 10),
                                   padx=10, pady=5)
        self.sig_button.pack(side='left')
        
        self.sig_label = tk.Label(sig_button_frame, 
                                 text="No file selected",
                                 font=('Arial', 9), fg='#7f8c8d')
        self.sig_label.pack(side='left', padx=(10, 0))
        
        # Certificate file selection
        cert_frame = tk.Frame(main_frame)
        cert_frame.pack(fill='x', pady=(0, 25))
        
        tk.Label(cert_frame, text="Signer's Certificate:", 
                font=('Arial', 10, 'bold')).pack(anchor='w')
        
        cert_button_frame = tk.Frame(cert_frame)
        cert_button_frame.pack(fill='x', pady=(5, 0))
        
        self.cert_button = tk.Button(cert_button_frame, 
                                   text="Select Certificate (.pem)",
                                   command=self.select_certificate,
                                   bg='#f39c12', fg='white',
                                   font=('Arial', 10),
                                   padx=10, pady=5)
        self.cert_button.pack(side='left')
        
        self.cert_label = tk.Label(cert_button_frame, 
                                 text="No file selected",
                                 font=('Arial', 9), fg='#7f8c8d')
        self.cert_label.pack(side='left', padx=(10, 0))
        
        # Verify button
        self.verify_button = tk.Button(main_frame, text="Verify",
                                     command=self.verify_document,
                                     bg='#9b59b6', fg='white',
                                     font=('Arial', 12, 'bold'),
                                     padx=30, pady=10)
        self.verify_button.pack(pady=(15, 20))
        
        # Result label
        self.result_label = tk.Label(main_frame, text="",
                                   font=('Arial', 14, 'bold'),
                                   height=2)
        self.result_label.pack()
        
        # Certificate info frame (initially hidden)
        self.info_frame = tk.Frame(main_frame)
        self.info_frame.pack(pady=(10, 0), fill='x')
        
        self.info_label = tk.Label(self.info_frame, text="",
                                 font=('Arial', 9),
                                 fg='#7f8c8d',
                                 justify='left')
        self.info_label.pack()
    
    def center_window(self):
        """Center the window on the screen"""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f'{width}x{height}+{x}+{y}')
    
    def select_document(self):
        """Handle document file selection"""
        file_path = filedialog.askopenfilename(
            title="Select Original Document",
            filetypes=[
                ("Text files", "*.txt"),
                ("PDF files", "*.pdf"),
                ("Word documents", "*.doc *.docx"),
                ("All files", "*.*")
            ],
            initialdir="signed_docs" if os.path.exists("signed_docs") else "."
        )
        
        if file_path:
            self.document_path = file_path
            filename = os.path.basename(file_path)
            self.doc_label.config(text=filename, fg='#27ae60')
    
    def select_signature(self):
        """Handle signature file selection"""
        file_path = filedialog.askopenfilename(
            title="Select Signature File",
            filetypes=[("Signature files", "*.sig"), ("All files", "*.*")],
            initialdir="signed_docs" if os.path.exists("signed_docs") else "."
        )
        
        if file_path:
            self.signature_path = file_path
            filename = os.path.basename(file_path)
            self.sig_label.config(text=filename, fg='#27ae60')
    
    def select_certificate(self):
        """Handle certificate file selection"""
        file_path = filedialog.askopenfilename(
            title="Select Signer's Certificate",
            filetypes=[("PEM files", "*.pem"), ("All files", "*.*")],
            initialdir="signed_docs" if os.path.exists("signed_docs") else "certs"
        )
        
        if file_path:
            self.certificate_path = file_path
            filename = os.path.basename(file_path)
            self.cert_label.config(text=filename, fg='#27ae60')
    
    def load_document(self, file_path):
        """Load document content and compute SHA-256 hash"""
        try:
            with open(file_path, 'rb') as f:
                content = f.read()
            document_hash = hashlib.sha256(content).digest()
            return content, document_hash
        except Exception as e:
            raise Exception(f"Failed to load document: {str(e)}")
    
    def load_signature(self, file_path):
        """Load digital signature from file"""
        try:
            with open(file_path, 'rb') as f:
                return f.read()
        except Exception as e:
            raise Exception(f"Failed to load signature: {str(e)}")
    
    def load_certificate(self, file_path):
        """Load certificate and extract public key"""
        try:
            with open(file_path, 'rb') as f:
                certificate = x509.load_pem_x509_certificate(f.read())
            public_key = certificate.public_key()
            return certificate, public_key
        except Exception as e:
            raise Exception(f"Failed to load certificate: {str(e)}")
    
    def verify_signature(self, public_key, document_hash, signature):
        """Verify the signature using the public key and document hash"""
        try:
            public_key.verify(
                signature,
                document_hash,
                padding.PSS(
                    mgf=padding.MGF1(hashes.SHA256()),
                    salt_length=padding.PSS.MAX_LENGTH
                ),
                hashes.SHA256()
            )
            return True
        except Exception:
            return False
    
    def get_certificate_info(self, certificate):
        """Extract information from certificate"""
        subject = certificate.subject
        
        info = {
            'common_name': None,
            'organization': None,
            'country': None,
            'not_valid_before': certificate.not_valid_before_utc,
            'not_valid_after': certificate.not_valid_after_utc,
            'serial_number': certificate.serial_number
        }
        
        # Extract subject information
        for attribute in subject:
            if attribute.oid == x509.NameOID.COMMON_NAME:
                info['common_name'] = attribute.value
            elif attribute.oid == x509.NameOID.ORGANIZATION_NAME:
                info['organization'] = attribute.value
            elif attribute.oid == x509.NameOID.COUNTRY_NAME:
                info['country'] = attribute.value
        
        return info
    
    def animate_result(self, text, color, is_valid=True):
        """Animate the result label with fade-in and color effects"""
        self.result_label.config(text=text, fg=color)
        
        if is_valid:
            # Fade-in animation for valid signature
            def fade_in():
                colors = ['#ffffff', '#e8f5e8', '#d4edda', '#c3e6cb', '#b8dcc6', '#27ae60']
                for fade_color in colors:
                    self.result_label.config(fg=fade_color)
                    time.sleep(0.1)
                self.result_label.config(fg=color)
            
            threading.Thread(target=fade_in, daemon=True).start()
        else:
            # Pulsing animation for invalid signature
            def pulse():
                for _ in range(4):
                    self.result_label.config(fg='#ffffff')
                    time.sleep(0.15)
                    self.result_label.config(fg=color)
                    time.sleep(0.15)
                self.result_label.config(fg=color)
            
            threading.Thread(target=pulse, daemon=True).start()
    
    def display_certificate_info(self, certificate):
        """Display certificate information"""
        cert_info = self.get_certificate_info(certificate)
        
        info_text = f"Certificate Information:\n"
        info_text += f"Signer: {cert_info['common_name']}\n"
        info_text += f"Organization: {cert_info['organization']}\n"
        info_text += f"Country: {cert_info['country']}\n"
        info_text += f"Valid from: {cert_info['not_valid_before'].strftime('%Y-%m-%d %H:%M:%S')}\n"
        info_text += f"Valid until: {cert_info['not_valid_after'].strftime('%Y-%m-%d %H:%M:%S')}"
        
        self.info_label.config(text=info_text)
    
    def verify_document(self):
        """Perform the document verification process"""
        # Validate file selections
        if not self.document_path:
            messagebox.showerror("Error", "Please select the original document!")
            return
        
        if not self.signature_path:
            messagebox.showerror("Error", "Please select the signature file!")
            return
        
        if not self.certificate_path:
            messagebox.showerror("Error", "Please select the signer's certificate!")
            return
        
        # Disable verify button during processing
        self.verify_button.config(state='disabled', text='Verifying...')
        self.result_label.config(text="", fg='black')
        self.info_label.config(text="")
        
        def verification_process():
            try:
                # Step 1: Load document and compute SHA-256 hash
                document_content, document_hash = self.load_document(self.document_path)
                
                # Step 2: Load digital signature
                signature = self.load_signature(self.signature_path)
                
                # Step 3: Load certificate and extract public key
                certificate, public_key = self.load_certificate(self.certificate_path)
                
                # Step 4: Verify signature using public key and hash
                verification_result = self.verify_signature(public_key, document_hash, signature)
                
                # Step 5: Display result with animation
                if verification_result:
                    self.animate_result("Valid Signature", "#27ae60", is_valid=True)
                    self.display_certificate_info(certificate)
                    
                    # Show success info
                    messagebox.showinfo("Verification Successful", 
                                      "✓ The document signature is valid!\n\n"
                                      "This confirms that:\n"
                                      "• The document has not been tampered with\n"
                                      "• The signature was created by the certificate holder\n"
                                      "• The document is authentic")
                else:
                    self.animate_result("Invalid Signature", "#e74c3c", is_valid=False)
                    self.display_certificate_info(certificate)
                    
                    # Show failure info
                    messagebox.showwarning("Verification Failed", 
                                         "✗ The document signature is invalid!\n\n"
                                         "This could mean:\n"
                                         "• The document has been modified\n"
                                         "• The signature doesn't match the document\n"
                                         "• Wrong certificate was used for verification")
                    
            except Exception as e:
                messagebox.showerror("Verification Error", str(e))
                self.result_label.config(text="Verification Error", fg="#e74c3c")
            
            finally:
                # Re-enable verify button
                self.verify_button.config(state='normal', text='Verify')
        
        # Run verification in separate thread to prevent GUI freezing
        threading.Thread(target=verification_process, daemon=True).start()


def verify_document_gui():
    """Main function to create and run the document verification GUI"""
    root = tk.Tk()
    app = PKIDocumentVerifierApp(root)
    root.mainloop()


if __name__ == "__main__":
    verify_document_gui()
