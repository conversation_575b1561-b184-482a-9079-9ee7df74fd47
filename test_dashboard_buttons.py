#!/usr/bin/env python3
"""
Simple test to verify dashboard button functionality
"""

import subprocess
import sys
import os


def test_individual_apps():
    """Test that individual applications can be launched"""
    print("Testing individual application launches...")
    
    apps = [
        ('pki_registration.py', 'Registration'),
        ('pki_login.py', 'Login'),
        ('pki_document_signer.py', 'Document Signer'),
        ('pki_document_verifier.py', 'Document Verifier'),
        ('legal_use_case.py', 'Legal Use Case')
    ]
    
    for app_file, app_name in apps:
        if os.path.exists(app_file):
            print(f"✓ {app_name} file exists: {app_file}")
            try:
                # Test if the app can be imported/executed
                result = subprocess.run([sys.executable, '-c', f'import {app_file[:-3]}'], 
                                      capture_output=True, text=True, timeout=5)
                if result.returncode == 0:
                    print(f"✓ {app_name} can be imported successfully")
                else:
                    print(f"⚠ {app_name} import issues: {result.stderr}")
            except Exception as e:
                print(f"⚠ {app_name} test error: {e}")
        else:
            print(f"✗ {app_name} file missing: {app_file}")
    
    print("\nAll applications should be launchable from the dashboard buttons.")


def check_demo_system():
    """Check if the demo system works"""
    print("\nTesting demo system (known working)...")
    
    if os.path.exists('demo_pki_system.py'):
        print("✓ Demo system exists")
        try:
            # Launch demo system briefly to test
            process = subprocess.Popen([sys.executable, 'demo_pki_system.py'])
            import time
            time.sleep(2)  # Let it start
            process.terminate()
            print("✓ Demo system launches successfully")
        except Exception as e:
            print(f"⚠ Demo system test error: {e}")
    else:
        print("✗ Demo system missing")


def main():
    """Main test function"""
    print("PKI Dashboard Button Functionality Test")
    print("=" * 45)
    
    test_individual_apps()
    check_demo_system()
    
    print("\n" + "=" * 45)
    print("BUTTON TESTING INSTRUCTIONS:")
    print("=" * 45)
    print("1. Run: python pki_main_dashboard.py")
    print("2. Click each button and verify:")
    print("   • Registration button → Opens registration window")
    print("   • Login button → Opens login window (or shows warning)")
    print("   • Sign Document → Opens signer (or shows warning)")
    print("   • Verify Document → Opens verifier (or shows warning)")
    print("   • View Use Cases → Opens legal use case window")
    print("\n3. Each button should:")
    print("   • Show an info popup with instructions")
    print("   • Launch the respective application window")
    print("   • Display the new window (check taskbar if not visible)")
    
    print("\nIf buttons still don't work:")
    print("• Check console for error messages")
    print("• Try the demo system: python demo_pki_system.py")
    print("• Verify all application files exist and are executable")


if __name__ == "__main__":
    main()
