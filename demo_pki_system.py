#!/usr/bin/env python3
"""
Demo script showing the complete PKI system workflow
"""

import tkinter as tk
from tkinter import messagebox
import os
import subprocess
import sys


class PKISystemDemo:
    def __init__(self, root):
        self.root = root
        self.setup_ui()
        
    def setup_ui(self):
        """Setup the demo interface"""
        self.root.title("PKI Document Signer - System Demo")
        self.root.geometry("450x350")
        self.root.resizable(False, False)
        
        # Center the window
        self.center_window()
        
        # Main frame
        main_frame = tk.Frame(self.root, padx=20, pady=20)
        main_frame.pack(expand=True, fill='both')
        
        # Title
        title_label = tk.Label(main_frame, 
                              text="PKI Document Signer\nSystem Demo", 
                              font=('Arial', 16, 'bold'), 
                              fg='#2c3e50',
                              justify='center')
        title_label.pack(pady=(0, 20))
        
        # Description
        desc_text = ("This demo shows the complete PKI workflow:\n"
                    "1. Register a new user (generate keys & certificate)\n"
                    "2. <PERSON>gin using the generated credentials\n"
                    "3. Sign documents with digital signatures")

        desc_label = tk.Label(main_frame, text=desc_text,
                             font=('Arial', 10),
                             justify='left',
                             wraplength=350)
        desc_label.pack(pady=(0, 20))
        
        # Buttons frame
        buttons_frame = tk.Frame(main_frame)
        buttons_frame.pack(pady=10)
        
        # Registration button
        self.register_button = tk.Button(buttons_frame, 
                                       text="1. Open Registration",
                                       command=self.open_registration,
                                       bg='#3498db', fg='white',
                                       font=('Arial', 11),
                                       padx=15, pady=8,
                                       width=20)
        self.register_button.pack(pady=(0, 10))
        
        # Login button
        self.login_button = tk.Button(buttons_frame,
                                    text="2. Open Login",
                                    command=self.open_login,
                                    bg='#27ae60', fg='white',
                                    font=('Arial', 11),
                                    padx=15, pady=8,
                                    width=20)
        self.login_button.pack(pady=(0, 10))

        # Document Signer button
        self.signer_button = tk.Button(buttons_frame,
                                     text="3. Open Document Signer",
                                     command=self.open_document_signer,
                                     bg='#f39c12', fg='white',
                                     font=('Arial', 11),
                                     padx=15, pady=8,
                                     width=20)
        self.signer_button.pack(pady=(0, 10))
        
        # Status frame
        status_frame = tk.Frame(main_frame)
        status_frame.pack(pady=(20, 0), fill='x')
        
        # Status info
        self.status_label = tk.Label(status_frame, 
                                   text=self.get_status_text(),
                                   font=('Arial', 9),
                                   fg='#7f8c8d',
                                   justify='left')
        self.status_label.pack()
        
        # Refresh button
        refresh_button = tk.Button(status_frame, 
                                 text="Refresh Status",
                                 command=self.refresh_status,
                                 font=('Arial', 8),
                                 padx=10, pady=2)
        refresh_button.pack(pady=(5, 0))
    
    def center_window(self):
        """Center the window on the screen"""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f'{width}x{height}+{x}+{y}')
    
    def get_status_text(self):
        """Get current system status"""
        status_lines = ["System Status:"]
        
        # Check for keys directory
        if os.path.exists('keys'):
            key_files = [f for f in os.listdir('keys') if f.endswith('_private.pem')]
            status_lines.append(f"• Keys directory: {len(key_files)} users registered")
        else:
            status_lines.append("• Keys directory: Not found")
        
        # Check for certs directory
        if os.path.exists('certs'):
            cert_files = [f for f in os.listdir('certs') if f.endswith('_cert.pem')]
            status_lines.append(f"• Certificates directory: {len(cert_files)} certificates")
        else:
            status_lines.append("• Certificates directory: Not found")

        # Check for signed_docs directory
        if os.path.exists('signed_docs'):
            signed_files = [f for f in os.listdir('signed_docs') if not f.endswith('.sig') and not f.endswith('_cert.pem')]
            status_lines.append(f"• Signed documents: {len(signed_files)} documents")
        else:
            status_lines.append("• Signed documents: None")

        return "\n".join(status_lines)
    
    def refresh_status(self):
        """Refresh the status display"""
        self.status_label.config(text=self.get_status_text())
    
    def open_registration(self):
        """Open the registration application"""
        try:
            subprocess.Popen([sys.executable, 'pki_registration.py'])
            messagebox.showinfo("Info", 
                              "Registration window opened!\n\n"
                              "Steps:\n"
                              "1. Enter a username\n"
                              "2. Click Register\n"
                              "3. Wait for success message")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to open registration: {str(e)}")
    
    def open_login(self):
        """Open the login application"""
        # Check if any users are registered
        if not os.path.exists('keys') or not os.path.exists('certs'):
            messagebox.showwarning("Warning", 
                                 "No registered users found!\n\n"
                                 "Please register a user first using the Registration window.")
            return
        
        key_files = [f for f in os.listdir('keys') if f.endswith('_private.pem')]
        if not key_files:
            messagebox.showwarning("Warning", 
                                 "No registered users found!\n\n"
                                 "Please register a user first using the Registration window.")
            return
        
        try:
            subprocess.Popen([sys.executable, 'pki_login.py'])
            
            # Show helpful instructions
            usernames = [f.replace('_private.pem', '') for f in key_files]
            username_list = '\n'.join([f"• {username}" for username in usernames[:5]])
            if len(usernames) > 5:
                username_list += f"\n... and {len(usernames) - 5} more"
            
            messagebox.showinfo("Info", 
                              f"Login window opened!\n\n"
                              f"Available users:\n{username_list}\n\n"
                              f"Steps:\n"
                              f"1. Select private key from keys/ folder\n"
                              f"2. Select certificate from certs/ folder\n"
                              f"3. Click Login to authenticate")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to open login: {str(e)}")

    def open_document_signer(self):
        """Open the document signer application"""
        # Check if any users are registered
        if not os.path.exists('keys') or not os.path.exists('certs'):
            messagebox.showwarning("Warning",
                                 "No registered users found!\n\n"
                                 "Please register a user first using the Registration window.")
            return

        key_files = [f for f in os.listdir('keys') if f.endswith('_private.pem')]
        if not key_files:
            messagebox.showwarning("Warning",
                                 "No registered users found!\n\n"
                                 "Please register a user first using the Registration window.")
            return

        try:
            subprocess.Popen([sys.executable, 'pki_document_signer.py'])

            # Show helpful instructions
            messagebox.showinfo("Info",
                              f"Document Signer window opened!\n\n"
                              f"Steps:\n"
                              f"1. Select a document to sign (any file type)\n"
                              f"2. Select private key from keys/ folder\n"
                              f"3. Select certificate from certs/ folder\n"
                              f"4. Click 'Sign Document'\n"
                              f"5. Check signed_docs/ folder for results\n\n"
                              f"Tip: Use sample_document.txt for testing!")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to open document signer: {str(e)}")


def main():
    """Main function to run the demo"""
    root = tk.Tk()
    app = PKISystemDemo(root)
    root.mainloop()


if __name__ == "__main__":
    main()
