#!/usr/bin/env python3
"""
Utility to verify signed documents
"""

import os
import sys
import hashlib
from cryptography.hazmat.primitives import serialization, hashes
from cryptography.hazmat.primitives.asymmetric import padding
from cryptography import x509


def load_document(doc_path):
    """Load document content"""
    try:
        with open(doc_path, 'rb') as f:
            return f.read()
    except Exception as e:
        raise Exception(f"Failed to load document: {str(e)}")


def load_signature(sig_path):
    """Load signature from file"""
    try:
        with open(sig_path, 'rb') as f:
            return f.read()
    except Exception as e:
        raise Exception(f"Failed to load signature: {str(e)}")


def load_certificate(cert_path):
    """Load certificate and extract public key"""
    try:
        with open(cert_path, 'rb') as f:
            certificate = x509.load_pem_x509_certificate(f.read())
        public_key = certificate.public_key()
        return certificate, public_key
    except Exception as e:
        raise Exception(f"Failed to load certificate: {str(e)}")


def verify_document_signature(document_content, signature, public_key):
    """Verify document signature"""
    try:
        # Hash the document content
        document_hash = hashlib.sha256(document_content).digest()
        
        # Verify signature
        public_key.verify(
            signature,
            document_hash,
            padding.PSS(
                mgf=padding.MGF1(hashes.SHA256()),
                salt_length=padding.PSS.MAX_LENGTH
            ),
            hashes.SHA256()
        )
        return True
    except Exception:
        return False


def get_certificate_info(certificate):
    """Extract information from certificate"""
    subject = certificate.subject
    issuer = certificate.issuer
    
    info = {
        'common_name': None,
        'organization': None,
        'country': None,
        'not_valid_before': certificate.not_valid_before,
        'not_valid_after': certificate.not_valid_after,
        'serial_number': certificate.serial_number
    }
    
    # Extract subject information
    for attribute in subject:
        if attribute.oid == x509.NameOID.COMMON_NAME:
            info['common_name'] = attribute.value
        elif attribute.oid == x509.NameOID.ORGANIZATION_NAME:
            info['organization'] = attribute.value
        elif attribute.oid == x509.NameOID.COUNTRY_NAME:
            info['country'] = attribute.value
    
    return info


def verify_signed_document_files(doc_path, sig_path, cert_path):
    """Verify a signed document with its signature and certificate"""
    print(f"Verifying signed document: {os.path.basename(doc_path)}")
    print("=" * 50)
    
    try:
        # Load all files
        print("Loading files...")
        document_content = load_document(doc_path)
        signature = load_signature(sig_path)
        certificate, public_key = load_certificate(cert_path)
        
        print(f"✓ Document loaded: {len(document_content)} bytes")
        print(f"✓ Signature loaded: {len(signature)} bytes")
        print(f"✓ Certificate loaded successfully")
        
        # Get certificate information
        cert_info = get_certificate_info(certificate)
        print(f"\nCertificate Information:")
        print(f"  Signer: {cert_info['common_name']}")
        print(f"  Organization: {cert_info['organization']}")
        print(f"  Country: {cert_info['country']}")
        print(f"  Valid from: {cert_info['not_valid_before']}")
        print(f"  Valid until: {cert_info['not_valid_after']}")
        print(f"  Serial number: {cert_info['serial_number']}")
        
        # Verify signature
        print(f"\nVerifying signature...")
        verification_result = verify_document_signature(document_content, signature, public_key)
        
        if verification_result:
            print("✓ SIGNATURE VERIFICATION SUCCESSFUL")
            print("  The document is authentic and has not been tampered with.")
            print("  The signature was created by the holder of the private key")
            print("  corresponding to the provided certificate.")
            return True
        else:
            print("✗ SIGNATURE VERIFICATION FAILED")
            print("  The document may have been tampered with, or")
            print("  the signature does not match the document content.")
            return False
            
    except Exception as e:
        print(f"✗ VERIFICATION ERROR: {str(e)}")
        return False


def find_signed_document_files(base_name, signed_docs_dir='signed_docs'):
    """Find all files related to a signed document"""
    if not os.path.exists(signed_docs_dir):
        raise Exception(f"Signed documents directory '{signed_docs_dir}' not found")
    
    # Look for files with the base name
    files = os.listdir(signed_docs_dir)
    
    doc_file = None
    sig_file = None
    cert_file = None
    
    for file in files:
        if file == base_name:
            doc_file = os.path.join(signed_docs_dir, file)
        elif file == f"{os.path.splitext(base_name)[0]}.sig":
            sig_file = os.path.join(signed_docs_dir, file)
        elif file == f"{os.path.splitext(base_name)[0]}_cert.pem":
            cert_file = os.path.join(signed_docs_dir, file)
    
    if not doc_file:
        raise Exception(f"Document file '{base_name}' not found in {signed_docs_dir}")
    if not sig_file:
        raise Exception(f"Signature file not found for '{base_name}'")
    if not cert_file:
        raise Exception(f"Certificate file not found for '{base_name}'")
    
    return doc_file, sig_file, cert_file


def list_signed_documents(signed_docs_dir='signed_docs'):
    """List all signed documents"""
    if not os.path.exists(signed_docs_dir):
        print(f"No signed documents directory found.")
        return []
    
    files = os.listdir(signed_docs_dir)
    
    # Find document files (exclude .sig and _cert.pem files)
    doc_files = []
    for file in files:
        if not file.endswith('.sig') and not file.endswith('_cert.pem'):
            doc_files.append(file)
    
    return doc_files


def main():
    """Main function"""
    if len(sys.argv) < 2:
        print("PKI Document Signature Verification Utility")
        print("=" * 45)
        print("\nUsage:")
        print("  python verify_signed_document.py <document_name>")
        print("  python verify_signed_document.py --list")
        print("\nExamples:")
        print("  python verify_signed_document.py sample_document.txt")
        print("  python verify_signed_document.py --list")
        print("\nThis utility verifies documents in the 'signed_docs/' folder.")
        return 1
    
    if sys.argv[1] == '--list':
        print("Available signed documents:")
        print("=" * 30)
        
        signed_docs = list_signed_documents()
        if signed_docs:
            for i, doc in enumerate(signed_docs, 1):
                print(f"{i}. {doc}")
        else:
            print("No signed documents found.")
        return 0
    
    document_name = sys.argv[1]
    
    try:
        # Find all related files
        doc_path, sig_path, cert_path = find_signed_document_files(document_name)
        
        # Verify the document
        success = verify_signed_document_files(doc_path, sig_path, cert_path)
        
        return 0 if success else 1
        
    except Exception as e:
        print(f"Error: {str(e)}")
        return 1


if __name__ == "__main__":
    exit(main())
