#!/usr/bin/env python3
"""
Test complete PKI workflow: Registration -> Login -> Document Signing -> Verification
"""

import os
import subprocess
import sys
import tkinter as tk
from pki_registration import PKIRegistrationApp
from pki_login import P<PERSON><PERSON><PERSON>in<PERSON><PERSON>
from pki_document_signer import PKIDocumentSignerApp
import verify_signed_document


def test_complete_workflow():
    """Test the complete PKI workflow"""
    print("Testing Complete PKI Workflow")
    print("=" * 40)
    
    test_username = "testworkflow"
    
    try:
        # Step 1: Registration
        print("\n1. Testing Registration...")
        root = tk.Tk()
        root.withdraw()
        
        registration_app = PKIRegistrationApp(root)
        registration_app.username_entry.insert(0, test_username)
        
        # Simulate registration process
        registration_app.create_directories()
        private_key, public_key = registration_app.generate_rsa_keypair()
        registration_app.save_private_key(private_key, test_username)
        registration_app.save_public_key(public_key, test_username)
        certificate = registration_app.generate_self_signed_certificate(private_key, public_key, test_username)
        registration_app.save_certificate(certificate, test_username)
        
        print(f"✓ User '{test_username}' registered successfully")
        root.destroy()
        
        # Step 2: Login Authentication
        print("\n2. Testing Login Authentication...")
        root = tk.Tk()
        root.withdraw()
        
        login_app = PKILoginApp(root)
        login_app.private_key_path = f'keys/{test_username}_private.pem'
        login_app.certificate_path = f'certs/{test_username}_cert.pem'
        
        # Test authentication process
        certificate, public_key = login_app.load_certificate(login_app.certificate_path)
        private_key = login_app.load_private_key(login_app.private_key_path)
        challenge = login_app.generate_challenge()
        signature = login_app.sign_challenge(private_key, challenge)
        verification_result = login_app.verify_signature(public_key, challenge, signature)
        
        assert verification_result == True, "Login authentication should succeed"
        print("✓ Login authentication successful")
        root.destroy()
        
        # Step 3: Document Signing
        print("\n3. Testing Document Signing...")
        root = tk.Tk()
        root.withdraw()
        
        signer_app = PKIDocumentSignerApp(root)
        signer_app.document_path = 'sample_document.txt'
        signer_app.private_key_path = f'keys/{test_username}_private.pem'
        signer_app.certificate_path = f'certs/{test_username}_cert.pem'
        
        # Test document signing process
        document_content = signer_app.read_document_content(signer_app.document_path)
        document_hash = signer_app.hash_content(document_content)
        private_key = signer_app.load_private_key(signer_app.private_key_path)
        signature = signer_app.sign_hash(private_key, document_hash)
        saved_paths = signer_app.save_signed_files(
            signer_app.document_path, 
            signature, 
            signer_app.certificate_path
        )
        
        print("✓ Document signed successfully")
        print(f"  Files saved: {len(saved_paths)} files")
        root.destroy()
        
        # Step 4: Document Verification
        print("\n4. Testing Document Verification...")
        
        # Find signed document files
        doc_path, sig_path, cert_path = verify_signed_document.find_signed_document_files('sample_document.txt')
        
        # Verify the document
        document_content = verify_signed_document.load_document(doc_path)
        signature = verify_signed_document.load_signature(sig_path)
        certificate, public_key = verify_signed_document.load_certificate(cert_path)
        
        verification_result = verify_signed_document.verify_document_signature(
            document_content, signature, public_key
        )
        
        assert verification_result == True, "Document verification should succeed"
        print("✓ Document verification successful")
        
        # Step 5: Test verification utility
        print("\n5. Testing Verification Utility...")
        
        # Test listing signed documents
        signed_docs = verify_signed_document.list_signed_documents()
        assert 'sample_document.txt' in signed_docs, "Sample document should be in signed documents list"
        print(f"✓ Found {len(signed_docs)} signed documents")
        
        print("\n✓ Complete PKI workflow test passed!")
        return True
        
    except Exception as e:
        print(f"\n✗ Workflow test failed: {str(e)}")
        return False
    
    finally:
        # Clean up test files
        cleanup_test_files(test_username)


def cleanup_test_files(username):
    """Clean up test files"""
    files_to_remove = [
        f'keys/{username}_private.pem',
        f'keys/{username}_public.pem',
        f'certs/{username}_cert.pem'
    ]
    
    for file_path in files_to_remove:
        if os.path.exists(file_path):
            os.remove(file_path)


def main():
    """Main function"""
    print("PKI Document Signer - Complete Workflow Test")
    print("=" * 50)
    
    success = test_complete_workflow()
    
    if success:
        print("\n🎉 All workflow tests completed successfully!")
        print("\nThe PKI Document Signer system is fully functional:")
        print("  ✓ User registration and key generation")
        print("  ✓ PKI-based authentication")
        print("  ✓ Document signing with digital signatures")
        print("  ✓ Signature verification and validation")
        
        print("\nNext steps:")
        print("  1. Run 'python demo_pki_system.py' for guided usage")
        print("  2. Use 'python verify_signed_document.py --list' to see signed documents")
        print("  3. Try signing your own documents!")
        
        return 0
    else:
        print("\n❌ Workflow test failed. Please check the error messages above.")
        return 1


if __name__ == "__main__":
    exit(main())
