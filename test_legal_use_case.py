#!/usr/bin/env python3
"""
Test script for Legal Use Case window
"""

import tkinter as tk
from legal_use_case import <PERSON><PERSON>se<PERSON>aseWindow


def test_legal_use_case_window():
    """Test the legal use case window functionality"""
    print("Testing Legal Use Case Window")
    print("=" * 35)
    
    try:
        # Create root window
        root = tk.Tk()
        root.withdraw()  # Hide for testing
        
        # Create the legal use case window
        app = LegalUseCaseWindow(root)
        
        # Test window properties
        assert root.title() == "Use Case - Legal Document Signing", "Window title should be correct"
        print("✓ Window title set correctly")
        
        # Test that text area exists and has content
        assert hasattr(app, 'text_area'), "Text area should exist"
        content = app.text_area.get('1.0', 'end')
        assert len(content.strip()) > 0, "Text area should have content"
        print("✓ Text area created with content")
        
        # Test that key sections are present in the content
        required_sections = [
            "Digital Contract Signing System",
            "OVERVIEW",
            "KEY FEATURES & BENEFITS",
            "REAL-WORLD APPLICATIONS",
            "TECHNICAL ADVANTAGES",
            "LEGAL COMPLIANCE",
            "SECURITY GUARANTEES"
        ]
        
        for section in required_sections:
            assert section in content, f"Section '{section}' should be present"
        print("✓ All required sections present")
        
        # Test that specific key points are mentioned
        key_points = [
            "private key",
            "digital certificate",
            "SHA-256",
            "RSA-PSS",
            "tamper-proof",
            "non-repudiation",
            "legal validity"
        ]
        
        content_lower = content.lower()
        for point in key_points:
            assert point.lower() in content_lower, f"Key point '{point}' should be mentioned"
        print("✓ All key technical points mentioned")
        
        # Test that text area is read-only
        assert str(app.text_area['state']) == 'disabled', "Text area should be read-only"
        print("✓ Text area is properly read-only")
        
        # Test formatting tags exist
        tag_names = app.text_area.tag_names()
        expected_tags = ['header', 'subheader', 'bullet']
        for tag in expected_tags:
            assert tag in tag_names, f"Formatting tag '{tag}' should exist"
        print("✓ Text formatting tags applied")
        
        root.destroy()
        print("✓ Legal use case window test passed")
        return True
        
    except Exception as e:
        print(f"✗ Legal use case window test failed: {str(e)}")
        return False


def test_content_quality():
    """Test the quality and completeness of the content"""
    print("\nTesting Content Quality")
    print("=" * 25)
    
    try:
        root = tk.Tk()
        root.withdraw()
        app = LegalUseCaseWindow(root)
        
        content = app.text_area.get('1.0', 'end')
        
        # Test content length (should be comprehensive)
        assert len(content) > 3000, "Content should be comprehensive (>3000 characters)"
        print(f"✓ Content length: {len(content)} characters")
        
        # Test for business applications
        business_terms = ["contract", "agreement", "NDA", "legal", "business", "government"]
        for term in business_terms:
            assert term.lower() in content.lower(), f"Business term '{term}' should be mentioned"
        print("✓ Business applications covered")
        
        # Test for security features
        security_terms = ["encryption", "authentication", "integrity", "security", "cryptographic"]
        for term in security_terms:
            assert term.lower() in content.lower(), f"Security term '{term}' should be mentioned"
        print("✓ Security features explained")
        
        # Test for legal compliance
        legal_terms = ["compliance", "E-SIGN", "UETA", "regulation", "jurisdiction"]
        found_legal_terms = sum(1 for term in legal_terms if term in content)
        assert found_legal_terms >= 3, "Should mention multiple legal compliance aspects"
        print("✓ Legal compliance information included")
        
        # Test for benefits
        benefit_terms = ["cost", "efficiency", "environmental", "paperless", "reduction"]
        found_benefits = sum(1 for term in benefit_terms if term.lower() in content.lower())
        assert found_benefits >= 3, "Should mention multiple benefits"
        print("✓ Benefits and advantages explained")
        
        root.destroy()
        print("✓ Content quality test passed")
        return True
        
    except Exception as e:
        print(f"✗ Content quality test failed: {str(e)}")
        return False


def test_ui_elements():
    """Test UI elements and functionality"""
    print("\nTesting UI Elements")
    print("=" * 20)
    
    try:
        root = tk.Tk()
        root.withdraw()
        app = LegalUseCaseWindow(root)
        
        # Test scrollable text area
        assert hasattr(app, 'text_area'), "Should have text area"
        assert app.text_area.winfo_class() == 'Text', "Should be a Text widget"
        print("✓ Scrollable text area present")
        
        # Test window is resizable
        assert root.resizable()[0] == True, "Window should be resizable horizontally"
        assert root.resizable()[1] == True, "Window should be resizable vertically"
        print("✓ Window is resizable")
        
        # Test window size is appropriate
        root.update_idletasks()
        width = root.winfo_width()
        height = root.winfo_height()
        assert width >= 600, "Window should be wide enough for content"
        assert height >= 400, "Window should be tall enough for content"
        print(f"✓ Window size appropriate: {width}x{height}")
        
        root.destroy()
        print("✓ UI elements test passed")
        return True
        
    except Exception as e:
        print(f"✗ UI elements test failed: {str(e)}")
        return False


def main():
    """Run all tests"""
    print("Legal Use Case Window - Testing")
    print("=" * 40)
    
    test1 = test_legal_use_case_window()
    test2 = test_content_quality()
    test3 = test_ui_elements()
    
    if test1 and test2 and test3:
        print("\n🎉 All legal use case tests passed!")
        print("\nThe Legal Use Case window is fully functional:")
        print("  ✓ Professional window layout and formatting")
        print("  ✓ Comprehensive content covering all key aspects")
        print("  ✓ Proper text formatting and styling")
        print("  ✓ Scrollable and resizable interface")
        print("  ✓ Business and legal applications explained")
        print("  ✓ Technical security features detailed")
        print("  ✓ Legal compliance information included")
        
        print("\nThe window effectively explains:")
        print("  • How PKI can be used for legal document signing")
        print("  • Real-world business applications")
        print("  • Security and compliance benefits")
        print("  • Technical implementation advantages")
        
        return 0
    else:
        print("\n❌ Some legal use case tests failed.")
        return 1


if __name__ == "__main__":
    exit(main())
