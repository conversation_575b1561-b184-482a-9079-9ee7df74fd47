import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import os
import subprocess
import sys
import threading
import time


class PKIMainDashboard:
    def __init__(self, root):
        self.root = root
        self.current_user = None
        self.is_logged_in = False
        self.private_key_path = None
        self.certificate_path = None
        self.setup_ui()

    def setup_ui(self):
        """Setup the main dashboard interface"""
        self.root.title("PKI Document Signing System - Professional Dashboard")
        self.root.geometry("1000x800")
        self.root.resizable(True, True)
        self.root.configure(bg='#2c3e50')

        # Center the window
        self.center_window()

        # Main container with gradient-like effect
        main_container = tk.Frame(self.root, bg='#34495e')
        main_container.pack(expand=True, fill='both', padx=20, pady=20)

        # Header
        self.create_header(main_container)

        # Main content area
        self.create_main_content(main_container)

        # Status bar
        self.create_status_bar(main_container)

        # Update status
        self.update_status()
    
    def center_window(self):
        """Center the window on the screen"""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f'{width}x{height}+{x}+{y}')
    
    def create_header(self, parent):
        """Create the professional header section"""
        header_frame = tk.Frame(parent, bg='#2c3e50', height=100)
        header_frame.pack(fill='x', pady=(0, 20))
        header_frame.pack_propagate(False)

        # Left side - Logo and title
        left_frame = tk.Frame(header_frame, bg='#2c3e50')
        left_frame.pack(side='left', fill='y', padx=30, pady=20)

        # Logo/Icon (using text for now)
        logo_label = tk.Label(left_frame, text="🔐", font=('Arial', 24),
                             fg='#3498db', bg='#2c3e50')
        logo_label.pack(side='left', padx=(0, 15))

        # Title and subtitle
        title_frame = tk.Frame(left_frame, bg='#2c3e50')
        title_frame.pack(side='left', fill='y')

        title_label = tk.Label(title_frame,
                              text="PKI Document Signing System",
                              font=('Arial', 18, 'bold'),
                              fg='white', bg='#2c3e50')
        title_label.pack(anchor='w')

        subtitle_label = tk.Label(title_frame,
                                 text="Professional Digital Signature Solution",
                                 font=('Arial', 10),
                                 fg='#bdc3c7', bg='#2c3e50')
        subtitle_label.pack(anchor='w')

        # Right side - Login status and user info
        right_frame = tk.Frame(header_frame, bg='#2c3e50')
        right_frame.pack(side='right', fill='y', padx=30, pady=20)

        # User status
        self.user_frame = tk.Frame(right_frame, bg='#2c3e50')
        self.user_frame.pack(side='right')

        self.login_status_label = tk.Label(self.user_frame,
                                         text="● Not Logged In",
                                         font=('Arial', 12, 'bold'),
                                         fg='#e74c3c', bg='#2c3e50')
        self.login_status_label.pack(anchor='e')

        self.user_info_label = tk.Label(self.user_frame,
                                       text="Please login to access signing features",
                                       font=('Arial', 9),
                                       fg='#95a5a6', bg='#2c3e50')
        self.user_info_label.pack(anchor='e')
    
    def create_main_content(self, parent):
        """Create the main content area with professional buttons"""
        content_frame = tk.Frame(parent, bg='#ecf0f1')
        content_frame.pack(expand=True, fill='both', pady=(0, 20))

        # Welcome section
        welcome_frame = tk.Frame(content_frame, bg='#ecf0f1')
        welcome_frame.pack(fill='x', padx=40, pady=30)

        welcome_label = tk.Label(welcome_frame,
                                text="Welcome to the PKI Document Signing System",
                                font=('Arial', 16, 'bold'),
                                fg='#2c3e50', bg='#ecf0f1')
        welcome_label.pack()

        desc_label = tk.Label(welcome_frame,
                             text="Secure digital document signing with enterprise-grade PKI technology",
                             font=('Arial', 11),
                             fg='#7f8c8d', bg='#ecf0f1')
        desc_label.pack(pady=(5, 0))

        # Main buttons grid
        buttons_frame = tk.Frame(content_frame, bg='#ecf0f1')
        buttons_frame.pack(expand=True, fill='both', padx=40, pady=20)

        # Configure grid
        buttons_frame.grid_columnconfigure(0, weight=1)
        buttons_frame.grid_columnconfigure(1, weight=1)
        buttons_frame.grid_rowconfigure(0, weight=1)
        buttons_frame.grid_rowconfigure(1, weight=1)
        buttons_frame.grid_rowconfigure(2, weight=1)

        # Create professional buttons
        self.create_professional_buttons(buttons_frame)

    def create_status_bar(self, parent):
        """Create the professional status bar"""
        status_frame = tk.Frame(parent, bg='#2c3e50', height=40)
        status_frame.pack(fill='x', side='bottom')
        status_frame.pack_propagate(False)

        # Left side status
        left_status = tk.Frame(status_frame, bg='#2c3e50')
        left_status.pack(side='left', fill='y', padx=20, pady=10)

        self.status_label = tk.Label(left_status,
                                   text="● Ready",
                                   font=('Arial', 10),
                                   fg='#27ae60', bg='#2c3e50')
        self.status_label.pack(side='left')

        # Right side system info
        right_status = tk.Frame(status_frame, bg='#2c3e50')
        right_status.pack(side='right', fill='y', padx=20, pady=10)

        self.info_label = tk.Label(right_status,
                                 text="",
                                 font=('Arial', 9),
                                 fg='#bdc3c7', bg='#2c3e50')
        self.info_label.pack(side='right')
    
    def create_professional_buttons(self, parent):
        """Create professional-looking buttons for each function"""

        # Button 1: User Registration
        reg_frame = tk.Frame(parent, bg='#3498db', relief='raised', bd=2)
        reg_frame.grid(row=0, column=0, padx=15, pady=15, sticky='nsew')

        reg_icon = tk.Label(reg_frame, text="👤", font=('Arial', 32),
                           fg='white', bg='#3498db')
        reg_icon.pack(pady=(20, 10))

        reg_title = tk.Label(reg_frame, text="User Registration",
                            font=('Arial', 14, 'bold'),
                            fg='white', bg='#3498db')
        reg_title.pack()

        reg_desc = tk.Label(reg_frame,
                           text="Create new user account\nGenerate RSA keys & certificate",
                           font=('Arial', 10), fg='#ecf0f1', bg='#3498db',
                           justify='center')
        reg_desc.pack(pady=(5, 15))

        reg_button = tk.Button(reg_frame, text="REGISTER USER",
                              command=self.open_registration,
                              bg='#2980b9', fg='white',
                              font=('Arial', 11, 'bold'),
                              padx=20, pady=8, relief='flat',
                              cursor='hand2')
        reg_button.pack(pady=(0, 20))

        # Button 2: User Login
        login_frame = tk.Frame(parent, bg='#f39c12', relief='raised', bd=2)
        login_frame.grid(row=0, column=1, padx=15, pady=15, sticky='nsew')

        login_icon = tk.Label(login_frame, text="🔑", font=('Arial', 32),
                             fg='white', bg='#f39c12')
        login_icon.pack(pady=(20, 10))

        login_title = tk.Label(login_frame, text="User Login",
                              font=('Arial', 14, 'bold'),
                              fg='white', bg='#f39c12')
        login_title.pack()

        login_desc = tk.Label(login_frame,
                             text="Authenticate with PKI\nChallenge-response verification",
                             font=('Arial', 10), fg='#fef9e7', bg='#f39c12',
                             justify='center')
        login_desc.pack(pady=(5, 15))

        self.login_button = tk.Button(login_frame, text="LOGIN",
                                     command=self.open_login,
                                     bg='#e67e22', fg='white',
                                     font=('Arial', 11, 'bold'),
                                     padx=20, pady=8, relief='flat',
                                     cursor='hand2')
        self.login_button.pack(pady=(0, 20))

        # Button 3: Document Signing (login required)
        sign_frame = tk.Frame(parent, bg='#27ae60', relief='raised', bd=2)
        sign_frame.grid(row=1, column=0, padx=15, pady=15, sticky='nsew')

        sign_icon = tk.Label(sign_frame, text="✍️", font=('Arial', 32),
                            fg='white', bg='#27ae60')
        sign_icon.pack(pady=(20, 10))

        sign_title = tk.Label(sign_frame, text="Sign Document",
                             font=('Arial', 14, 'bold'),
                             fg='white', bg='#27ae60')
        sign_title.pack()

        sign_desc = tk.Label(sign_frame,
                            text="Digitally sign documents\nSHA-256 + RSA-PSS signatures",
                            font=('Arial', 10), fg='#e8f8f5', bg='#27ae60',
                            justify='center')
        sign_desc.pack(pady=(5, 15))

        self.sign_button = tk.Button(sign_frame, text="SIGN DOCUMENT",
                                    command=self.open_document_signer,
                                    bg='#229954', fg='white',
                                    font=('Arial', 11, 'bold'),
                                    padx=20, pady=8, relief='flat',
                                    cursor='hand2', state='disabled')
        self.sign_button.pack(pady=(0, 20))

        # Lock icon for disabled state
        self.lock_label = tk.Label(sign_frame, text="🔒 Login Required",
                                  font=('Arial', 9), fg='#a9dfbf', bg='#27ae60')
        self.lock_label.pack()

        # Button 4: Document Verification
        verify_frame = tk.Frame(parent, bg='#9b59b6', relief='raised', bd=2)
        verify_frame.grid(row=1, column=1, padx=15, pady=15, sticky='nsew')

        verify_icon = tk.Label(verify_frame, text="✅", font=('Arial', 32),
                              fg='white', bg='#9b59b6')
        verify_icon.pack(pady=(20, 10))

        verify_title = tk.Label(verify_frame, text="Verify Document",
                               font=('Arial', 14, 'bold'),
                               fg='white', bg='#9b59b6')
        verify_title.pack()

        verify_desc = tk.Label(verify_frame,
                              text="Verify digital signatures\nAuthenticity & integrity check",
                              font=('Arial', 10), fg='#f4ecf7', bg='#9b59b6',
                              justify='center')
        verify_desc.pack(pady=(5, 15))

        verify_button = tk.Button(verify_frame, text="VERIFY DOCUMENT",
                                 command=self.open_document_verifier,
                                 bg='#8e44ad', fg='white',
                                 font=('Arial', 11, 'bold'),
                                 padx=20, pady=8, relief='flat',
                                 cursor='hand2')
        verify_button.pack(pady=(0, 20))

        # Button 5: Use Cases (spans both columns)
        usecase_frame = tk.Frame(parent, bg='#34495e', relief='raised', bd=2)
        usecase_frame.grid(row=2, column=0, columnspan=2, padx=15, pady=15, sticky='nsew')

        usecase_content = tk.Frame(usecase_frame, bg='#34495e')
        usecase_content.pack(expand=True, fill='both')

        usecase_icon = tk.Label(usecase_content, text="📋", font=('Arial', 24),
                               fg='white', bg='#34495e')
        usecase_icon.pack(pady=(15, 5))

        usecase_title = tk.Label(usecase_content, text="Legal Use Cases & Information",
                                font=('Arial', 14, 'bold'),
                                fg='white', bg='#34495e')
        usecase_title.pack()

        usecase_desc = tk.Label(usecase_content,
                               text="Learn about real-world applications • Legal contracts • Business agreements • Compliance requirements",
                               font=('Arial', 10), fg='#d5dbdb', bg='#34495e',
                               justify='center')
        usecase_desc.pack(pady=(5, 10))

        usecase_button = tk.Button(usecase_content, text="VIEW USE CASES",
                                  command=self.open_legal_usecase,
                                  bg='#2c3e50', fg='white',
                                  font=('Arial', 11, 'bold'),
                                  padx=30, pady=8, relief='flat',
                                  cursor='hand2')
        usecase_button.pack(pady=(0, 15))
    
    def open_registration(self):
        """Open the registration window"""
        try:
            subprocess.Popen([sys.executable, 'pki_registration.py'])
            self.update_status_message("Registration window opened", '#3498db')
            messagebox.showinfo("Registration",
                              "Registration window opened!\n\n"
                              "Steps:\n"
                              "1. Enter a username\n"
                              "2. Click 'Register'\n"
                              "3. Wait for success message\n\n"
                              "This will generate your RSA key pair and certificate.")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to open registration: {str(e)}")
            self.update_status_message("Failed to open registration", '#e74c3c')
    
    def open_login(self):
        """Open the login window"""
        try:
            subprocess.Popen([sys.executable, 'pki_login.py'])
            self.update_status_message("Login window opened", '#f39c12')

            # Create a dialog to simulate login process
            self.create_login_dialog()

        except Exception as e:
            messagebox.showerror("Error", f"Failed to open login: {str(e)}")
            self.update_status_message("Failed to open login", '#e74c3c')

    def create_login_dialog(self):
        """Create a login dialog for the dashboard"""
        login_dialog = tk.Toplevel(self.root)
        login_dialog.title("Quick Login")
        login_dialog.geometry("400x300")
        login_dialog.resizable(False, False)
        login_dialog.configure(bg='#ecf0f1')
        login_dialog.transient(self.root)
        login_dialog.grab_set()

        # Center the dialog
        login_dialog.update_idletasks()
        x = (login_dialog.winfo_screenwidth() // 2) - (login_dialog.winfo_width() // 2)
        y = (login_dialog.winfo_screenheight() // 2) - (login_dialog.winfo_height() // 2)
        login_dialog.geometry(f'+{x}+{y}')

        # Title
        title_label = tk.Label(login_dialog, text="Quick Login for Dashboard",
                              font=('Arial', 14, 'bold'), fg='#2c3e50', bg='#ecf0f1')
        title_label.pack(pady=20)

        # Instructions
        inst_label = tk.Label(login_dialog,
                             text="Select your private key and certificate files\nto enable document signing features",
                             font=('Arial', 10), fg='#7f8c8d', bg='#ecf0f1',
                             justify='center')
        inst_label.pack(pady=(0, 20))

        # File selection
        file_frame = tk.Frame(login_dialog, bg='#ecf0f1')
        file_frame.pack(pady=10)

        # Private key selection
        tk.Button(file_frame, text="Select Private Key (.pem)",
                 command=self.select_private_key_for_login,
                 bg='#3498db', fg='white', font=('Arial', 10),
                 padx=15, pady=5).pack(pady=5)

        self.login_key_label = tk.Label(file_frame, text="No private key selected",
                                       font=('Arial', 9), fg='#7f8c8d', bg='#ecf0f1')
        self.login_key_label.pack(pady=(0, 10))

        # Certificate selection
        tk.Button(file_frame, text="Select Certificate (.pem)",
                 command=self.select_cert_for_login,
                 bg='#f39c12', fg='white', font=('Arial', 10),
                 padx=15, pady=5).pack(pady=5)

        self.login_cert_label = tk.Label(file_frame, text="No certificate selected",
                                        font=('Arial', 9), fg='#7f8c8d', bg='#ecf0f1')
        self.login_cert_label.pack(pady=(0, 20))

        # Login button
        login_btn = tk.Button(file_frame, text="LOGIN TO DASHBOARD",
                             command=lambda: self.perform_dashboard_login(login_dialog),
                             bg='#27ae60', fg='white', font=('Arial', 11, 'bold'),
                             padx=20, pady=8)
        login_btn.pack(pady=10)

        # Cancel button
        cancel_btn = tk.Button(file_frame, text="Cancel",
                              command=login_dialog.destroy,
                              bg='#95a5a6', fg='white', font=('Arial', 10),
                              padx=15, pady=5)
        cancel_btn.pack(pady=5)
    
    def select_private_key_for_login(self):
        """Select private key for dashboard login"""
        file_path = filedialog.askopenfilename(
            title="Select Private Key",
            filetypes=[("PEM files", "*.pem")],
            initialdir="keys" if os.path.exists("keys") else "."
        )
        if file_path:
            self.private_key_path = file_path
            filename = os.path.basename(file_path)
            self.login_key_label.config(text=f"✓ {filename}", fg='#27ae60')

    def select_cert_for_login(self):
        """Select certificate for dashboard login"""
        file_path = filedialog.askopenfilename(
            title="Select Certificate",
            filetypes=[("PEM files", "*.pem")],
            initialdir="certs" if os.path.exists("certs") else "."
        )
        if file_path:
            self.certificate_path = file_path
            filename = os.path.basename(file_path)
            self.login_cert_label.config(text=f"✓ {filename}", fg='#27ae60')

    def perform_dashboard_login(self, dialog):
        """Perform login for dashboard access"""
        if not self.private_key_path or not self.certificate_path:
            messagebox.showerror("Error", "Please select both private key and certificate files!")
            return

        try:
            # Extract username from private key filename
            key_filename = os.path.basename(self.private_key_path)
            if '_private.pem' in key_filename:
                self.current_user = key_filename.replace('_private.pem', '')
            else:
                self.current_user = "User"

            # Simulate successful login
            self.is_logged_in = True

            # Update UI
            self.login_status_label.config(text=f"● Logged in as: {self.current_user}", fg='#27ae60')
            self.user_info_label.config(text="Document signing features enabled")

            # Enable sign button
            self.sign_button.config(state='normal', cursor='hand2')
            self.lock_label.config(text="🔓 Ready to Sign", fg='#d5f4e6')

            # Close dialog
            dialog.destroy()

            # Show success message
            self.update_status_message(f"Logged in as {self.current_user}", '#27ae60')
            messagebox.showinfo("Login Successful",
                              f"Welcome {self.current_user}!\n\n"
                              "You can now access document signing features.\n"
                              "The 'Sign Document' button is now enabled.")

        except Exception as e:
            messagebox.showerror("Login Error", f"Login failed: {str(e)}")

    def open_document_signer(self):
        """Open the document signer (requires login)"""
        if not self.is_logged_in:
            messagebox.showwarning("Login Required",
                                 "Please login first to access document signing features!\n\n"
                                 "Click the 'LOGIN' button to authenticate.")
            return

        try:
            subprocess.Popen([sys.executable, 'pki_document_signer.py'])
            self.update_status_message("Document signer opened", '#27ae60')
            messagebox.showinfo("Document Signer",
                              "Document signer window opened!\n\n"
                              "Steps:\n"
                              "1. Select document to sign\n"
                              "2. Select your private key\n"
                              "3. Select your certificate\n"
                              "4. Click 'Sign Document'\n\n"
                              "Files will be saved to signed_docs/ folder.")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to open document signer: {str(e)}")
            self.update_status_message("Failed to open document signer", '#e74c3c')
    
    def open_document_verifier(self):
        """Open the document verifier"""
        try:
            subprocess.Popen([sys.executable, 'pki_document_verifier.py'])
            self.update_status_message("Document verifier opened", '#9b59b6')
            messagebox.showinfo("Document Verifier",
                              "Document verifier window opened!\n\n"
                              "Steps:\n"
                              "1. Select original document\n"
                              "2. Select signature file (.sig)\n"
                              "3. Select certificate file (.pem)\n"
                              "4. Click 'Verify'\n\n"
                              "The system will check document authenticity.")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to open document verifier: {str(e)}")
            self.update_status_message("Failed to open document verifier", '#e74c3c')

    def open_legal_usecase(self):
        """Open the legal use case window"""
        try:
            subprocess.Popen([sys.executable, 'legal_use_case.py'])
            self.update_status_message("Legal use case opened", '#34495e')
            messagebox.showinfo("Legal Use Cases",
                              "Legal use case window opened!\n\n"
                              "Learn about:\n"
                              "• Digital contract signing\n"
                              "• Legal compliance requirements\n"
                              "• Business applications\n"
                              "• Security guarantees\n\n"
                              "Understand real-world PKI applications.")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to open legal use case: {str(e)}")
            self.update_status_message("Failed to open legal use case", '#e74c3c')
    
    def update_status_message(self, message, color='#27ae60'):
        """Update the status message with color"""
        self.status_label.config(text=f"● {message}", fg=color)

        # Auto-reset to ready after 5 seconds using tkinter's after method
        def reset_status():
            try:
                self.status_label.config(text="● Ready", fg='#27ae60')
            except:
                pass  # Ignore if window is destroyed

        self.root.after(5000, reset_status)
    
    def update_status(self):
        """Update system status information"""
        info_parts = []

        if os.path.exists('keys'):
            key_count = len([f for f in os.listdir('keys') if f.endswith('_private.pem')])
            info_parts.append(f"Registered Users: {key_count}")
        else:
            info_parts.append("Registered Users: 0")

        if os.path.exists('signed_docs'):
            doc_count = len([f for f in os.listdir('signed_docs') if not f.endswith('.sig') and not f.endswith('_cert.pem')])
            info_parts.append(f"Signed Documents: {doc_count}")
        else:
            info_parts.append("Signed Documents: 0")

        # Add login status
        if self.is_logged_in:
            info_parts.append(f"Current User: {self.current_user}")

        self.info_label.config(text=" | ".join(info_parts))

        # Schedule next update
        self.root.after(5000, self.update_status)  # Update every 5 seconds


def main():
    """Main function to run the dashboard"""
    root = tk.Tk()
    app = PKIMainDashboard(root)
    root.mainloop()


if __name__ == "__main__":
    main()
