import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import os
import subprocess
import sys
import threading
import time


class PKIMainDashboard:
    def __init__(self, root):
        self.root = root
        self.current_user = None
        self.is_logged_in = False
        self.setup_ui()

    def setup_ui(self):
        """Setup the main dashboard interface"""
        self.root.title("PKI Document Signing System - Professional Dashboard")
        self.root.geometry("1000x800")
        self.root.resizable(True, True)
        self.root.configure(bg='#2c3e50')

        # Center the window
        self.center_window()

        # Main container with gradient-like effect
        main_container = tk.Frame(self.root, bg='#34495e')
        main_container.pack(expand=True, fill='both', padx=20, pady=20)

        # Header
        self.create_header(main_container)

        # Main content area
        self.create_main_content(main_container)

        # Status bar
        self.create_status_bar(main_container)

        # Update status
        self.update_status()
    
    def center_window(self):
        """Center the window on the screen"""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f'{width}x{height}+{x}+{y}')
    
    def create_header(self, parent):
        """Create the professional header section"""
        header_frame = tk.Frame(parent, bg='#2c3e50', height=100)
        header_frame.pack(fill='x', pady=(0, 20))
        header_frame.pack_propagate(False)

        # Left side - Logo and title
        left_frame = tk.Frame(header_frame, bg='#2c3e50')
        left_frame.pack(side='left', fill='y', padx=30, pady=20)

        # Logo/Icon (using text for now)
        logo_label = tk.Label(left_frame, text="🔐", font=('Arial', 24),
                             fg='#3498db', bg='#2c3e50')
        logo_label.pack(side='left', padx=(0, 15))

        # Title and subtitle
        title_frame = tk.Frame(left_frame, bg='#2c3e50')
        title_frame.pack(side='left', fill='y')

        title_label = tk.Label(title_frame,
                              text="PKI Document Signing System",
                              font=('Arial', 18, 'bold'),
                              fg='white', bg='#2c3e50')
        title_label.pack(anchor='w')

        subtitle_label = tk.Label(title_frame,
                                 text="Professional Digital Signature Solution",
                                 font=('Arial', 10),
                                 fg='#bdc3c7', bg='#2c3e50')
        subtitle_label.pack(anchor='w')

        # Right side - Login status and user info
        right_frame = tk.Frame(header_frame, bg='#2c3e50')
        right_frame.pack(side='right', fill='y', padx=30, pady=20)

        # User status
        self.user_frame = tk.Frame(right_frame, bg='#2c3e50')
        self.user_frame.pack(side='right')

        self.login_status_label = tk.Label(self.user_frame,
                                         text="● Not Logged In",
                                         font=('Arial', 12, 'bold'),
                                         fg='#e74c3c', bg='#2c3e50')
        self.login_status_label.pack(anchor='e')

        self.user_info_label = tk.Label(self.user_frame,
                                       text="Please login to access signing features",
                                       font=('Arial', 9),
                                       fg='#95a5a6', bg='#2c3e50')
        self.user_info_label.pack(anchor='e')
    
    def create_main_content(self, parent):
        """Create the main content area with professional buttons"""
        content_frame = tk.Frame(parent, bg='#ecf0f1')
        content_frame.pack(expand=True, fill='both', pady=(0, 20))

        # Welcome section
        welcome_frame = tk.Frame(content_frame, bg='#ecf0f1')
        welcome_frame.pack(fill='x', padx=40, pady=30)

        welcome_label = tk.Label(welcome_frame,
                                text="Welcome to the PKI Document Signing System",
                                font=('Arial', 16, 'bold'),
                                fg='#2c3e50', bg='#ecf0f1')
        welcome_label.pack()

        desc_label = tk.Label(welcome_frame,
                             text="Secure digital document signing with enterprise-grade PKI technology",
                             font=('Arial', 11),
                             fg='#7f8c8d', bg='#ecf0f1')
        desc_label.pack(pady=(5, 0))

        # Main scrollable buttons area
        scroll_container = tk.Frame(content_frame, bg='#ecf0f1')
        scroll_container.pack(expand=True, fill='both', padx=20, pady=20)

        # Create scrollable canvas
        self.canvas = tk.Canvas(scroll_container, bg='#ecf0f1', highlightthickness=0)
        scrollbar = tk.Scrollbar(scroll_container, orient="vertical", command=self.canvas.yview)
        self.scrollable_frame = tk.Frame(self.canvas, bg='#ecf0f1')

        # Configure scrolling
        self.scrollable_frame.bind(
            "<Configure>",
            lambda e: self.canvas.configure(scrollregion=self.canvas.bbox("all"))
        )

        self.canvas_window = self.canvas.create_window((0, 0), window=self.scrollable_frame, anchor="nw")
        self.canvas.configure(yscrollcommand=scrollbar.set)

        # Bind canvas resize event
        self.canvas.bind("<Configure>", self._on_canvas_configure)

        # Pack canvas and scrollbar
        self.canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        # Bind mouse wheel and keyboard scrolling
        self.canvas.bind("<MouseWheel>", self._on_mousewheel)
        self.root.bind("<MouseWheel>", self._on_mousewheel)

        # Bind keyboard scrolling
        self.canvas.bind("<Up>", lambda e: self.canvas.yview_scroll(-1, "units"))
        self.canvas.bind("<Down>", lambda e: self.canvas.yview_scroll(1, "units"))
        self.canvas.bind("<Prior>", lambda e: self.canvas.yview_scroll(-1, "pages"))  # Page Up
        self.canvas.bind("<Next>", lambda e: self.canvas.yview_scroll(1, "pages"))   # Page Down

        # Make canvas focusable for keyboard events
        self.canvas.focus_set()

        # Add scroll instruction at the top
        scroll_hint = tk.Label(self.scrollable_frame,
                              text="💡 Use mouse wheel or arrow keys to scroll if content extends below",
                              font=('Arial', 9), fg='#7f8c8d', bg='#ecf0f1',
                              pady=5)
        scroll_hint.pack(pady=(10, 0))

        # Create professional buttons in scrollable frame
        self.create_professional_buttons(self.scrollable_frame)

    def _on_mousewheel(self, event):
        """Handle mouse wheel scrolling"""
        self.canvas.yview_scroll(int(-1*(event.delta/120)), "units")

    def _on_canvas_configure(self, event):
        """Handle canvas resize to update scroll region"""
        # Update the canvas scroll region when the frame changes size
        self.canvas.configure(scrollregion=self.canvas.bbox("all"))

        # Update the canvas window width to match the canvas width
        canvas_width = event.width
        self.canvas.itemconfig(self.canvas_window, width=canvas_width)

    def create_status_bar(self, parent):
        """Create the professional status bar"""
        status_frame = tk.Frame(parent, bg='#2c3e50', height=40)
        status_frame.pack(fill='x', side='bottom')
        status_frame.pack_propagate(False)

        # Left side status
        left_status = tk.Frame(status_frame, bg='#2c3e50')
        left_status.pack(side='left', fill='y', padx=20, pady=10)

        self.status_label = tk.Label(left_status,
                                   text="● Ready",
                                   font=('Arial', 10),
                                   fg='#27ae60', bg='#2c3e50')
        self.status_label.pack(side='left')

        # Right side system info
        right_status = tk.Frame(status_frame, bg='#2c3e50')
        right_status.pack(side='right', fill='y', padx=20, pady=10)

        self.info_label = tk.Label(right_status,
                                 text="",
                                 font=('Arial', 9),
                                 fg='#bdc3c7', bg='#2c3e50')
        self.info_label.pack(side='right')
    
    def create_professional_buttons(self, parent):
        """Create professional-looking buttons for each function"""

        # Create a simple button frame (like demo system)
        button_container = tk.Frame(parent, bg='#ecf0f1')
        button_container.pack(expand=True, fill='both', padx=40, pady=40)

        # Button 1: User Registration
        reg_frame = tk.Frame(button_container, bg='#3498db', relief='raised', bd=2)
        reg_frame.pack(fill='x', pady=10)

        reg_content = tk.Frame(reg_frame, bg='#3498db')
        reg_content.pack(expand=True, fill='both', padx=20, pady=15)

        reg_icon = tk.Label(reg_content, text="👤", font=('Arial', 24),
                           fg='white', bg='#3498db')
        reg_icon.pack(side='left', padx=(0, 15))

        reg_info = tk.Frame(reg_content, bg='#3498db')
        reg_info.pack(side='left', expand=True, fill='both')

        reg_title = tk.Label(reg_info, text="User Registration",
                            font=('Arial', 14, 'bold'),
                            fg='white', bg='#3498db')
        reg_title.pack(anchor='w')

        reg_desc = tk.Label(reg_info,
                           text="Create new user account • Generate RSA keys & certificate",
                           font=('Arial', 10), fg='#ecf0f1', bg='#3498db')
        reg_desc.pack(anchor='w')

        reg_button = tk.Button(reg_content, text="REGISTER USER",
                              command=self.open_registration,
                              bg='#2980b9', fg='white',
                              font=('Arial', 11, 'bold'),
                              padx=20, pady=8, relief='flat',
                              cursor='hand2')
        reg_button.pack(side='right', padx=(15, 0))

        # Button 2: User Login
        login_frame = tk.Frame(button_container, bg='#f39c12', relief='raised', bd=2)
        login_frame.pack(fill='x', pady=10)

        login_content = tk.Frame(login_frame, bg='#f39c12')
        login_content.pack(expand=True, fill='both', padx=20, pady=15)

        login_icon = tk.Label(login_content, text="🔑", font=('Arial', 24),
                             fg='white', bg='#f39c12')
        login_icon.pack(side='left', padx=(0, 15))

        login_info = tk.Frame(login_content, bg='#f39c12')
        login_info.pack(side='left', expand=True, fill='both')

        login_title = tk.Label(login_info, text="User Login",
                              font=('Arial', 14, 'bold'),
                              fg='white', bg='#f39c12')
        login_title.pack(anchor='w')

        login_desc = tk.Label(login_info,
                             text="Authenticate with PKI • Challenge-response verification",
                             font=('Arial', 10), fg='#fef9e7', bg='#f39c12')
        login_desc.pack(anchor='w')

        self.login_button = tk.Button(login_content, text="LOGIN",
                                     command=self.open_login,
                                     bg='#e67e22', fg='white',
                                     font=('Arial', 11, 'bold'),
                                     padx=20, pady=8, relief='flat',
                                     cursor='hand2')
        self.login_button.pack(side='right', padx=(15, 0))

        # Button 3: Document Signing (login required)
        sign_frame = tk.Frame(button_container, bg='#27ae60', relief='raised', bd=2)
        sign_frame.pack(fill='x', pady=10)

        sign_content = tk.Frame(sign_frame, bg='#27ae60')
        sign_content.pack(expand=True, fill='both', padx=20, pady=15)

        sign_icon = tk.Label(sign_content, text="✍️", font=('Arial', 24),
                            fg='white', bg='#27ae60')
        sign_icon.pack(side='left', padx=(0, 15))

        sign_info = tk.Frame(sign_content, bg='#27ae60')
        sign_info.pack(side='left', expand=True, fill='both')

        sign_title = tk.Label(sign_info, text="Sign Document",
                             font=('Arial', 14, 'bold'),
                             fg='white', bg='#27ae60')
        sign_title.pack(anchor='w')

        sign_desc = tk.Label(sign_info,
                            text="Digitally sign documents • SHA-256 + RSA-PSS signatures",
                            font=('Arial', 10), fg='#e8f8f5', bg='#27ae60')
        sign_desc.pack(anchor='w')

        # Lock icon for disabled state
        self.lock_label = tk.Label(sign_info, text="🔒 Login Required",
                                  font=('Arial', 9), fg='#a9dfbf', bg='#27ae60')
        self.lock_label.pack(anchor='w')

        self.sign_button = tk.Button(sign_content, text="SIGN DOCUMENT",
                                    command=self.open_document_signer,
                                    bg='#229954', fg='white',
                                    font=('Arial', 11, 'bold'),
                                    padx=20, pady=8, relief='flat',
                                    cursor='hand2', state='disabled')
        self.sign_button.pack(side='right', padx=(15, 0))

        # Button 4: Document Verification
        verify_frame = tk.Frame(button_container, bg='#9b59b6', relief='raised', bd=2)
        verify_frame.pack(fill='x', pady=10)

        verify_content = tk.Frame(verify_frame, bg='#9b59b6')
        verify_content.pack(expand=True, fill='both', padx=20, pady=15)

        verify_icon = tk.Label(verify_content, text="✅", font=('Arial', 24),
                              fg='white', bg='#9b59b6')
        verify_icon.pack(side='left', padx=(0, 15))

        verify_info = tk.Frame(verify_content, bg='#9b59b6')
        verify_info.pack(side='left', expand=True, fill='both')

        verify_title = tk.Label(verify_info, text="Verify Document",
                               font=('Arial', 14, 'bold'),
                               fg='white', bg='#9b59b6')
        verify_title.pack(anchor='w')

        verify_desc = tk.Label(verify_info,
                              text="Verify digital signatures • Authenticity & integrity check",
                              font=('Arial', 10), fg='#f4ecf7', bg='#9b59b6')
        verify_desc.pack(anchor='w')

        verify_button = tk.Button(verify_content, text="VERIFY DOCUMENT",
                                 command=self.open_document_verifier,
                                 bg='#8e44ad', fg='white',
                                 font=('Arial', 11, 'bold'),
                                 padx=20, pady=8, relief='flat',
                                 cursor='hand2')
        verify_button.pack(side='right', padx=(15, 0))

        # Button 5: Use Cases
        usecase_frame = tk.Frame(button_container, bg='#34495e', relief='raised', bd=2)
        usecase_frame.pack(fill='x', pady=10)

        usecase_content = tk.Frame(usecase_frame, bg='#34495e')
        usecase_content.pack(expand=True, fill='both', padx=20, pady=15)

        usecase_icon = tk.Label(usecase_content, text="📋", font=('Arial', 24),
                               fg='white', bg='#34495e')
        usecase_icon.pack(side='left', padx=(0, 15))

        usecase_info = tk.Frame(usecase_content, bg='#34495e')
        usecase_info.pack(side='left', expand=True, fill='both')

        usecase_title = tk.Label(usecase_info, text="Legal Use Cases & Information",
                                font=('Arial', 14, 'bold'),
                                fg='white', bg='#34495e')
        usecase_title.pack(anchor='w')

        usecase_desc = tk.Label(usecase_info,
                               text="Learn about real-world applications • Legal contracts • Business agreements",
                               font=('Arial', 10), fg='#d5dbdb', bg='#34495e')
        usecase_desc.pack(anchor='w')

        usecase_button = tk.Button(usecase_content, text="VIEW USE CASES",
                                  command=self.open_legal_usecase,
                                  bg='#2c3e50', fg='white',
                                  font=('Arial', 11, 'bold'),
                                  padx=30, pady=8, relief='flat',
                                  cursor='hand2')
        usecase_button.pack(side='right', padx=(15, 0))
    
    def open_registration(self):
        """Open the registration application"""
        try:
            subprocess.Popen([sys.executable, 'pki_registration.py'])
            messagebox.showinfo("Info",
                              "Registration window opened!\n\n"
                              "Steps:\n"
                              "1. Enter a username\n"
                              "2. Click Register\n"
                              "3. Wait for success message")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to open registration: {str(e)}")
    
    def open_login(self):
        """Open the login application"""
        # Check if any users are registered
        if not os.path.exists('keys') or not os.path.exists('certs'):
            messagebox.showwarning("Warning",
                                 "No registered users found!\n\n"
                                 "Please register a user first using the Registration window.")
            return

        key_files = [f for f in os.listdir('keys') if f.endswith('_private.pem')]
        if not key_files:
            messagebox.showwarning("Warning",
                                 "No registered users found!\n\n"
                                 "Please register a user first using the Registration window.")
            return

        try:
            subprocess.Popen([sys.executable, 'pki_login.py'])

            # Show helpful instructions
            usernames = [f.replace('_private.pem', '') for f in key_files]
            username_list = '\n'.join([f"• {username}" for username in usernames[:5]])
            if len(usernames) > 5:
                username_list += f"\n... and {len(usernames) - 5} more"

            messagebox.showinfo("Info",
                              f"Login window opened!\n\n"
                              f"Available users:\n{username_list}\n\n"
                              f"Steps:\n"
                              f"1. Select private key from keys/ folder\n"
                              f"2. Select certificate from certs/ folder\n"
                              f"3. Click Login to authenticate")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to open login: {str(e)}")



    def open_document_signer(self):
        """Open the document signer application"""
        # Check if any users are registered
        if not os.path.exists('keys') or not os.path.exists('certs'):
            messagebox.showwarning("Warning",
                                 "No registered users found!\n\n"
                                 "Please register a user first using the Registration window.")
            return

        key_files = [f for f in os.listdir('keys') if f.endswith('_private.pem')]
        if not key_files:
            messagebox.showwarning("Warning",
                                 "No registered users found!\n\n"
                                 "Please register a user first using the Registration window.")
            return

        try:
            subprocess.Popen([sys.executable, 'pki_document_signer.py'])

            # Show helpful instructions
            messagebox.showinfo("Info",
                              f"Document Signer window opened!\n\n"
                              f"Steps:\n"
                              f"1. Select a document to sign (any file type)\n"
                              f"2. Select private key from keys/ folder\n"
                              f"3. Select certificate from certs/ folder\n"
                              f"4. Click 'Sign Document'\n"
                              f"5. Check signed_docs/ folder for results\n\n"
                              f"Tip: Use sample_document.txt for testing!")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to open document signer: {str(e)}")
    
    def open_document_verifier(self):
        """Open the document verifier application"""
        # Check if any signed documents exist
        if not os.path.exists('signed_docs'):
            messagebox.showwarning("Warning",
                                 "No signed documents found!\n\n"
                                 "Please sign a document first using the Document Signer.")
            return

        signed_files = [f for f in os.listdir('signed_docs') if not f.endswith('.sig') and not f.endswith('_cert.pem')]
        if not signed_files:
            messagebox.showwarning("Warning",
                                 "No signed documents found!\n\n"
                                 "Please sign a document first using the Document Signer.")
            return

        try:
            subprocess.Popen([sys.executable, 'pki_document_verifier.py'])

            # Show helpful instructions
            messagebox.showinfo("Info",
                              f"Document Verifier window opened!\n\n"
                              f"Steps:\n"
                              f"1. Select original document from signed_docs/\n"
                              f"2. Select signature file (.sig) from signed_docs/\n"
                              f"3. Select certificate file (_cert.pem) from signed_docs/\n"
                              f"4. Click 'Verify' to check authenticity\n\n"
                              f"Available documents: {len(signed_files)}")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to open document verifier: {str(e)}")

    def open_legal_usecase(self):
        """Open the legal use case window"""
        try:
            subprocess.Popen([sys.executable, 'legal_use_case.py'])

            messagebox.showinfo("Info",
                              "Legal Use Case window opened!\n\n"
                              "This window explains how the PKI system can be used\n"
                              "for legal document signing in real-world scenarios.\n\n"
                              "Learn about:\n"
                              "• Digital contract signing\n"
                              "• Legal compliance requirements\n"
                              "• Business applications\n"
                              "• Security guarantees")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to open legal use case: {str(e)}")
    

    
    def update_status(self):
        """Update system status information"""
        info_parts = []

        if os.path.exists('keys'):
            key_count = len([f for f in os.listdir('keys') if f.endswith('_private.pem')])
            info_parts.append(f"Registered Users: {key_count}")
        else:
            info_parts.append("Registered Users: 0")

        if os.path.exists('signed_docs'):
            doc_count = len([f for f in os.listdir('signed_docs') if not f.endswith('.sig') and not f.endswith('_cert.pem')])
            info_parts.append(f"Signed Documents: {doc_count}")
        else:
            info_parts.append("Signed Documents: 0")

        # Add login status
        if self.is_logged_in:
            info_parts.append(f"Current User: {self.current_user}")

        self.info_label.config(text=" | ".join(info_parts))

        # Schedule next update
        self.root.after(5000, self.update_status)  # Update every 5 seconds


def main():
    """Main function to run the dashboard"""
    root = tk.Tk()
    app = PKIMainDashboard(root)
    root.mainloop()


if __name__ == "__main__":
    main()
