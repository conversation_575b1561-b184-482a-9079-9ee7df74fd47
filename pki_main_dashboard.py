import tkinter as tk
from tkinter import ttk, messagebox
import os
import subprocess
import sys
import threading
import time


class PKIMainDashboard:
    def __init__(self, root):
        self.root = root
        self.current_user = None
        self.is_logged_in = False
        self.setup_ui()
        
    def setup_ui(self):
        """Setup the main dashboard interface"""
        self.root.title("PKI Document Signing System - Main Dashboard")
        self.root.geometry("900x700")
        self.root.resizable(True, True)
        
        # Center the window
        self.center_window()
        
        # Configure style
        style = ttk.Style()
        style.theme_use('clam')
        
        # Main container
        main_container = tk.Frame(self.root, bg='#f8f9fa')
        main_container.pack(expand=True, fill='both', padx=10, pady=10)
        
        # Header
        self.create_header(main_container)
        
        # Status bar
        self.create_status_bar(main_container)
        
        # Main content area with tabs
        self.create_tabbed_interface(main_container)
        
        # Update status
        self.update_status()
    
    def center_window(self):
        """Center the window on the screen"""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f'{width}x{height}+{x}+{y}')
    
    def create_header(self, parent):
        """Create the header section"""
        header_frame = tk.Frame(parent, bg='#2c3e50', height=80)
        header_frame.pack(fill='x', pady=(0, 10))
        header_frame.pack_propagate(False)
        
        # Title
        title_label = tk.Label(header_frame, 
                              text="PKI Document Signing System",
                              font=('Arial', 20, 'bold'),
                              fg='white', bg='#2c3e50')
        title_label.pack(side='left', padx=20, pady=20)
        
        # Login status
        self.login_status_label = tk.Label(header_frame,
                                         text="Not Logged In",
                                         font=('Arial', 12),
                                         fg='#e74c3c', bg='#2c3e50')
        self.login_status_label.pack(side='right', padx=20, pady=20)
    
    def create_status_bar(self, parent):
        """Create the status bar"""
        status_frame = tk.Frame(parent, bg='#ecf0f1', height=30)
        status_frame.pack(fill='x', side='bottom')
        status_frame.pack_propagate(False)
        
        self.status_label = tk.Label(status_frame,
                                   text="Ready",
                                   font=('Arial', 9),
                                   fg='#2c3e50', bg='#ecf0f1')
        self.status_label.pack(side='left', padx=10, pady=5)
        
        # System info
        self.info_label = tk.Label(status_frame,
                                 text="",
                                 font=('Arial', 9),
                                 fg='#7f8c8d', bg='#ecf0f1')
        self.info_label.pack(side='right', padx=10, pady=5)
    
    def create_tabbed_interface(self, parent):
        """Create the main tabbed interface"""
        # Create notebook for tabs
        self.notebook = ttk.Notebook(parent)
        self.notebook.pack(expand=True, fill='both', pady=(0, 10))
        
        # Tab 1: Registration
        self.create_registration_tab()
        
        # Tab 2: Login
        self.create_login_tab()
        
        # Tab 3: Document Signing (disabled until login)
        self.create_signing_tab()
        
        # Tab 4: Document Verification
        self.create_verification_tab()
        
        # Tab 5: Use Case Information
        self.create_usecase_tab()
        
        # Initially disable signing tab
        self.notebook.tab(2, state='disabled')
        
        # Bind tab change event
        self.notebook.bind('<<NotebookTabChanged>>', self.on_tab_changed)
    
    def create_registration_tab(self):
        """Create the registration tab"""
        reg_frame = ttk.Frame(self.notebook)
        self.notebook.add(reg_frame, text="1. Register")
        
        # Title
        title = tk.Label(reg_frame, text="User Registration",
                        font=('Arial', 16, 'bold'), fg='#2c3e50')
        title.pack(pady=20)
        
        # Description
        desc = tk.Label(reg_frame, 
                       text="Register a new user to generate RSA key pair and digital certificate",
                       font=('Arial', 11), fg='#7f8c8d', wraplength=600)
        desc.pack(pady=(0, 30))
        
        # Registration form
        form_frame = tk.Frame(reg_frame)
        form_frame.pack(pady=20)
        
        tk.Label(form_frame, text="Username:", font=('Arial', 12, 'bold')).pack(anchor='w')
        self.username_entry = tk.Entry(form_frame, font=('Arial', 11), width=30)
        self.username_entry.pack(pady=(5, 20))
        
        self.register_button = tk.Button(form_frame, text="Register User",
                                       command=self.register_user,
                                       bg='#3498db', fg='white',
                                       font=('Arial', 12, 'bold'),
                                       padx=30, pady=10)
        self.register_button.pack()
        
        # Result area
        self.reg_result_label = tk.Label(reg_frame, text="", font=('Arial', 12, 'bold'))
        self.reg_result_label.pack(pady=20)
    
    def create_login_tab(self):
        """Create the login tab"""
        login_frame = ttk.Frame(self.notebook)
        self.notebook.add(login_frame, text="2. Login")
        
        # Title
        title = tk.Label(login_frame, text="User Authentication",
                        font=('Arial', 16, 'bold'), fg='#2c3e50')
        title.pack(pady=20)
        
        # Description
        desc = tk.Label(login_frame,
                       text="Login using your private key and certificate for challenge-response authentication",
                       font=('Arial', 11), fg='#7f8c8d', wraplength=600)
        desc.pack(pady=(0, 30))
        
        # Login form
        form_frame = tk.Frame(login_frame)
        form_frame.pack(pady=20)
        
        # File selection buttons
        self.create_file_selector(form_frame, "Private Key:", "Select Private Key (.pem)",
                                self.select_private_key, '#3498db')
        self.private_key_label = tk.Label(form_frame, text="No file selected",
                                        font=('Arial', 9), fg='#7f8c8d')
        self.private_key_label.pack(pady=(0, 15))
        
        self.create_file_selector(form_frame, "Certificate:", "Select Certificate (.pem)",
                                self.select_certificate, '#f39c12')
        self.cert_label = tk.Label(form_frame, text="No file selected",
                                 font=('Arial', 9), fg='#7f8c8d')
        self.cert_label.pack(pady=(0, 20))
        
        self.login_button = tk.Button(form_frame, text="Login",
                                    command=self.perform_login,
                                    bg='#27ae60', fg='white',
                                    font=('Arial', 12, 'bold'),
                                    padx=30, pady=10)
        self.login_button.pack()
        
        # Result area
        self.login_result_label = tk.Label(login_frame, text="", font=('Arial', 12, 'bold'))
        self.login_result_label.pack(pady=20)
    
    def create_signing_tab(self):
        """Create the document signing tab"""
        sign_frame = ttk.Frame(self.notebook)
        self.notebook.add(sign_frame, text="3. Sign Document")
        
        # Title
        title = tk.Label(sign_frame, text="Document Signing",
                        font=('Arial', 16, 'bold'), fg='#2c3e50')
        title.pack(pady=20)
        
        # Description
        desc = tk.Label(sign_frame,
                       text="Sign documents with your digital signature for authenticity and integrity",
                       font=('Arial', 11), fg='#7f8c8d', wraplength=600)
        desc.pack(pady=(0, 30))
        
        # Signing form
        form_frame = tk.Frame(sign_frame)
        form_frame.pack(pady=20)
        
        self.create_file_selector(form_frame, "Document:", "Select Document to Sign",
                                self.select_document, '#e74c3c')
        self.doc_label = tk.Label(form_frame, text="No file selected",
                                font=('Arial', 9), fg='#7f8c8d')
        self.doc_label.pack(pady=(0, 20))
        
        self.sign_button = tk.Button(form_frame, text="Sign Document",
                                   command=self.sign_document,
                                   bg='#9b59b6', fg='white',
                                   font=('Arial', 12, 'bold'),
                                   padx=30, pady=10)
        self.sign_button.pack()
        
        # Result area
        self.sign_result_label = tk.Label(sign_frame, text="", font=('Arial', 12, 'bold'))
        self.sign_result_label.pack(pady=20)
    
    def create_verification_tab(self):
        """Create the document verification tab"""
        verify_frame = ttk.Frame(self.notebook)
        self.notebook.add(verify_frame, text="4. Verify Document")
        
        # Title
        title = tk.Label(verify_frame, text="Document Verification",
                        font=('Arial', 16, 'bold'), fg='#2c3e50')
        title.pack(pady=20)
        
        # Description
        desc = tk.Label(verify_frame,
                       text="Verify the authenticity and integrity of signed documents",
                       font=('Arial', 11), fg='#7f8c8d', wraplength=600)
        desc.pack(pady=(0, 30))
        
        # Quick verify button for signed docs
        quick_frame = tk.Frame(verify_frame)
        quick_frame.pack(pady=10)
        
        tk.Button(quick_frame, text="Open Document Verifier",
                 command=self.open_verifier,
                 bg='#16a085', fg='white',
                 font=('Arial', 11),
                 padx=20, pady=8).pack()
    
    def create_usecase_tab(self):
        """Create the use case information tab"""
        usecase_frame = ttk.Frame(self.notebook)
        self.notebook.add(usecase_frame, text="5. Use Cases")
        
        # Title
        title = tk.Label(usecase_frame, text="Real-World Applications",
                        font=('Arial', 16, 'bold'), fg='#2c3e50')
        title.pack(pady=20)
        
        # Description
        desc = tk.Label(usecase_frame,
                       text="Learn how PKI document signing is used in legal and business scenarios",
                       font=('Arial', 11), fg='#7f8c8d', wraplength=600)
        desc.pack(pady=(0, 30))
        
        # Use case button
        tk.Button(usecase_frame, text="View Legal Use Cases",
                 command=self.open_legal_usecase,
                 bg='#34495e', fg='white',
                 font=('Arial', 12, 'bold'),
                 padx=30, pady=10).pack()
    
    def create_file_selector(self, parent, label_text, button_text, command, color):
        """Helper to create file selector widgets"""
        tk.Label(parent, text=label_text, font=('Arial', 10, 'bold')).pack(anchor='w')
        tk.Button(parent, text=button_text, command=command,
                 bg=color, fg='white', font=('Arial', 10),
                 padx=10, pady=5).pack(pady=(5, 0))
    
    def register_user(self):
        """Handle user registration"""
        username = self.username_entry.get().strip()
        if not username:
            messagebox.showerror("Error", "Please enter a username!")
            return
        
        self.status_label.config(text="Registering user...")
        self.register_button.config(state='disabled', text='Registering...')
        
        def registration_process():
            try:
                # Launch registration app
                result = subprocess.run([sys.executable, 'pki_registration.py'], 
                                      capture_output=True, text=True, timeout=30)
                
                if result.returncode == 0:
                    self.animate_result(self.reg_result_label, "Registration completed! Check the registration window.", '#27ae60')
                    self.update_status()
                else:
                    self.reg_result_label.config(text="Registration failed!", fg='#e74c3c')
                    
            except Exception as e:
                self.reg_result_label.config(text=f"Error: {str(e)}", fg='#e74c3c')
            finally:
                self.register_button.config(state='normal', text='Register User')
                self.status_label.config(text="Ready")
        
        threading.Thread(target=registration_process, daemon=True).start()
    
    def select_private_key(self):
        """Select private key file"""
        from tkinter import filedialog
        file_path = filedialog.askopenfilename(
            title="Select Private Key",
            filetypes=[("PEM files", "*.pem")],
            initialdir="keys" if os.path.exists("keys") else "."
        )
        if file_path:
            self.private_key_path = file_path
            self.private_key_label.config(text=os.path.basename(file_path), fg='#27ae60')
    
    def select_certificate(self):
        """Select certificate file"""
        from tkinter import filedialog
        file_path = filedialog.askopenfilename(
            title="Select Certificate",
            filetypes=[("PEM files", "*.pem")],
            initialdir="certs" if os.path.exists("certs") else "."
        )
        if file_path:
            self.certificate_path = file_path
            self.cert_label.config(text=os.path.basename(file_path), fg='#27ae60')
    
    def select_document(self):
        """Select document to sign"""
        from tkinter import filedialog
        file_path = filedialog.askopenfilename(
            title="Select Document to Sign",
            filetypes=[("All files", "*.*")]
        )
        if file_path:
            self.document_path = file_path
            self.doc_label.config(text=os.path.basename(file_path), fg='#27ae60')
    
    def perform_login(self):
        """Handle user login"""
        if not hasattr(self, 'private_key_path') or not hasattr(self, 'certificate_path'):
            messagebox.showerror("Error", "Please select both private key and certificate files!")
            return
        
        # Launch login app for authentication
        try:
            subprocess.Popen([sys.executable, 'pki_login.py'])
            self.animate_result(self.login_result_label, "Login window opened! Complete authentication there.", '#3498db')
            
            # Simulate successful login for demo (in real app, this would be handled by the login app)
            self.root.after(3000, self.simulate_login_success)
            
        except Exception as e:
            messagebox.showerror("Error", f"Failed to open login: {str(e)}")
    
    def simulate_login_success(self):
        """Simulate successful login (for demo purposes)"""
        self.is_logged_in = True
        self.current_user = "demo_user"
        self.login_status_label.config(text=f"Logged in as: {self.current_user}", fg='#27ae60')
        self.notebook.tab(2, state='normal')  # Enable signing tab
        self.animate_result(self.login_result_label, "Login Successful! You can now sign documents.", '#27ae60')
    
    def sign_document(self):
        """Handle document signing"""
        if not self.is_logged_in:
            messagebox.showerror("Error", "Please login first!")
            return
        
        if not hasattr(self, 'document_path'):
            messagebox.showerror("Error", "Please select a document to sign!")
            return
        
        try:
            subprocess.Popen([sys.executable, 'pki_document_signer.py'])
            self.animate_result(self.sign_result_label, "Document signer opened! Complete signing there.", '#9b59b6')
        except Exception as e:
            messagebox.showerror("Error", f"Failed to open document signer: {str(e)}")
    
    def open_verifier(self):
        """Open document verifier"""
        try:
            subprocess.Popen([sys.executable, 'pki_document_verifier.py'])
        except Exception as e:
            messagebox.showerror("Error", f"Failed to open verifier: {str(e)}")
    
    def open_legal_usecase(self):
        """Open legal use case window"""
        try:
            subprocess.Popen([sys.executable, 'legal_use_case.py'])
        except Exception as e:
            messagebox.showerror("Error", f"Failed to open use case: {str(e)}")
    
    def animate_result(self, label, text, color):
        """Animate result message"""
        label.config(text=text, fg=color)
        
        def fade_in():
            for alpha in ['#ffffff', '#f8f9fa', color]:
                label.config(fg=alpha)
                time.sleep(0.1)
        
        threading.Thread(target=fade_in, daemon=True).start()
    
    def update_status(self):
        """Update system status information"""
        info_parts = []
        
        if os.path.exists('keys'):
            key_count = len([f for f in os.listdir('keys') if f.endswith('_private.pem')])
            info_parts.append(f"Users: {key_count}")
        
        if os.path.exists('signed_docs'):
            doc_count = len([f for f in os.listdir('signed_docs') if not f.endswith('.sig') and not f.endswith('_cert.pem')])
            info_parts.append(f"Signed Docs: {doc_count}")
        
        self.info_label.config(text=" | ".join(info_parts))
    
    def on_tab_changed(self, event):
        """Handle tab change events"""
        selected_tab = event.widget.tab('current')['text']
        self.status_label.config(text=f"Current: {selected_tab}")


def main():
    """Main function to run the dashboard"""
    root = tk.Tk()
    app = PKIMainDashboard(root)
    root.mainloop()


if __name__ == "__main__":
    main()
