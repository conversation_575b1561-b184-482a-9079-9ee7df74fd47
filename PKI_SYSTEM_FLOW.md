# PKI Document Signing System - Complete Flow Guide

## System Architecture Overview

The PKI Document Signing System is a comprehensive GUI-based application built with Python Tkinter and the cryptography library. It provides a complete workflow for secure document signing using Public Key Infrastructure (PKI).

## Component Structure

```
PKI Document Signing System
├── Main Dashboard (pki_main_dashboard.py)
│   ├── Registration Tab
│   ├── Login Tab  
│   ├── Document Signing Tab (login-protected)
│   ├── Document Verification Tab
│   └── Use Case Information Tab
├── Individual Applications
│   ├── Registration App (pki_registration.py)
│   ├── Login App (pki_login.py)
│   ├── Document Signer (pki_document_signer.py)
│   ├── Document Verifier (pki_document_verifier.py)
│   └── Legal Use Case (legal_use_case.py)
└── Command Line Tools
    └── Verification Utility (verify_signed_document.py)
```

## Complete Workflow

### 1. User Registration Flow

**Entry Point**: Main Dashboard → Registration Tab OR `python pki_registration.py`

**Process**:
1. User enters username in text field
2. System validates username (alphanumeric + hyphens/underscores)
3. On "Register" click:
   - Generate 2048-bit RSA key pair using `cryptography.hazmat.primitives.asymmetric.rsa`
   - Create self-signed X.509 certificate with user's public key
   - Save private key to `keys/<username>_private.pem`
   - Save public key to `keys/<username>_public.pem`
   - Save certificate to `certs/<username>_cert.pem`
4. Show animated success message with fade-in effect
5. Create directories automatically using `os.makedirs()`

**Key Code Components**:
```python
# Key Generation
private_key = rsa.generate_private_key(
    public_exponent=65537,
    key_size=2048
)

# Certificate Creation
cert = x509.CertificateBuilder().subject_name(subject)
    .issuer_name(issuer)
    .public_key(public_key)
    .serial_number(x509.random_serial_number())
    .not_valid_before(datetime.datetime.utcnow())
    .not_valid_after(datetime.datetime.utcnow() + datetime.timedelta(days=365))
    .sign(private_key, hashes.SHA256())
```

### 2. User Login Flow

**Entry Point**: Main Dashboard → Login Tab OR `python pki_login.py`

**Process**:
1. User selects private key file (.pem) from `keys/` folder
2. User selects certificate file (.pem) from `certs/` folder
3. On "Login" click:
   - Load certificate and extract public key
   - Load private key from file
   - Generate random 32-character challenge string
   - Sign challenge using private key with RSA-PSS padding
   - Verify signature using public key from certificate
4. If verification succeeds:
   - Show "Login Successful" with green fade-in animation
   - Enable access to document signing features
5. If verification fails:
   - Show "Authentication Failed" in red with blink animation

**Key Code Components**:
```python
# Challenge Generation
challenge = ''.join(secrets.choice(string.ascii_letters + string.digits) for _ in range(32))

# Digital Signing
signature = private_key.sign(
    challenge.encode('utf-8'),
    padding.PSS(
        mgf=padding.MGF1(hashes.SHA256()),
        salt_length=padding.PSS.MAX_LENGTH
    ),
    hashes.SHA256()
)

# Signature Verification
public_key.verify(signature, challenge.encode('utf-8'), padding.PSS(...), hashes.SHA256())
```

### 3. Document Signing Flow

**Entry Point**: Main Dashboard → Sign Document Tab (login required) OR `python pki_document_signer.py`

**Process**:
1. User selects document file (any type: .txt, .pdf, .doc, etc.)
2. User selects their private key file (.pem)
3. User selects their certificate file (.pem)
4. On "Sign Document" click:
   - Read document content as binary data
   - Compute SHA-256 hash of document content
   - Sign hash using private key with RSA-PSS
   - Create `signed_docs/` directory if it doesn't exist
   - Save original document to `signed_docs/<filename>`
   - Save signature to `signed_docs/<filename_without_ext>.sig`
   - Save certificate copy to `signed_docs/<filename_without_ext>_cert.pem`
5. Show "Document signed successfully!" with fade-in animation
6. Display info popup with saved file details

**Key Code Components**:
```python
# Document Hashing
document_hash = hashlib.sha256(document_content).digest()

# Digital Signing
signature = private_key.sign(
    document_hash,
    padding.PSS(
        mgf=padding.MGF1(hashes.SHA256()),
        salt_length=padding.PSS.MAX_LENGTH
    ),
    hashes.SHA256()
)

# File Organization
shutil.copy2(document_path, signed_doc_path)
with open(signature_path, 'wb') as f:
    f.write(signature)
```

### 4. Document Verification Flow

**Entry Point**: Main Dashboard → Verify Document Tab OR `python pki_document_verifier.py`

**Process**:
1. User selects original document file
2. User selects signature file (.sig)
3. User selects signer's certificate file (.pem)
4. On "Verify" click:
   - Load document and compute SHA-256 hash
   - Load digital signature from .sig file
   - Load certificate and extract public key
   - Verify signature using public key and document hash
5. Display result:
   - "Valid Signature" in green with fade-in animation if verification passes
   - "Invalid Signature" in red with pulse animation if verification fails
6. Show certificate information (signer name, validity period, etc.)
7. Display detailed popup with verification results

**Key Code Components**:
```python
# Document Hash Verification
document_hash = hashlib.sha256(document_content).digest()

# Signature Verification
try:
    public_key.verify(
        signature,
        document_hash,
        padding.PSS(
            mgf=padding.MGF1(hashes.SHA256()),
            salt_length=padding.PSS.MAX_LENGTH
        ),
        hashes.SHA256()
    )
    return True  # Valid signature
except:
    return False  # Invalid signature
```

### 5. Use Case Information Flow

**Entry Point**: Main Dashboard → Use Cases Tab OR `python legal_use_case.py`

**Process**:
1. Display comprehensive information about PKI document signing
2. Cover real-world applications:
   - Legal contracts and agreements
   - Business documents and NDAs
   - Government forms and compliance
3. Explain security benefits and legal compliance
4. Provide "Open Demo System" button for hands-on experience

## Navigation and Access Control

### Main Dashboard Features:
- **Tabbed Interface**: Clean navigation between all functions
- **Login Status**: Header shows current login state
- **Access Control**: Document signing tab disabled until successful login
- **Status Bar**: Shows current activity and system statistics
- **Responsive Design**: Resizable interface with proper scaling

### Security Features:
- **Challenge-Response Authentication**: Prevents replay attacks
- **RSA-PSS Signatures**: Industry-standard signature scheme
- **SHA-256 Hashing**: Secure document integrity verification
- **Certificate Validation**: X.509 certificate standard compliance
- **File Organization**: Secure separation of keys, certificates, and signed documents

## File Structure After Complete Workflow:

```
project_directory/
├── keys/
│   ├── <username>_private.pem    # User's private key
│   └── <username>_public.pem     # User's public key
├── certs/
│   └── <username>_cert.pem       # User's certificate
├── signed_docs/
│   ├── <document_name>           # Original signed document
│   ├── <document>.sig            # Digital signature
│   └── <document>_cert.pem       # Signer's certificate copy
└── Applications/
    ├── pki_main_dashboard.py     # Main navigation interface
    ├── pki_registration.py       # User registration
    ├── pki_login.py              # User authentication
    ├── pki_document_signer.py    # Document signing
    ├── pki_document_verifier.py  # Document verification
    └── legal_use_case.py         # Use case information
```

## UI Design Principles:

### Clean Interface:
- Consistent color scheme (#2c3e50, #3498db, #27ae60, #e74c3c)
- Professional typography (Arial font family)
- Proper spacing and padding
- Intuitive button placement

### Smooth Transitions:
- Fade-in animations for success messages
- Color transitions for status changes
- Threading for non-blocking operations
- Progress indicators during processing

### Clear Labels:
- Descriptive button text
- Helpful tooltips and descriptions
- Status messages and error handling
- File selection feedback

### Modular Functions:
- Separate classes for each component
- Reusable UI helper functions
- Clean separation of concerns
- Easy maintenance and extension

## Getting Started:

1. **Run Main Dashboard**: `python pki_main_dashboard.py`
2. **Register User**: Use Registration tab to create credentials
3. **Login**: Authenticate using Login tab
4. **Sign Documents**: Use Sign Document tab (requires login)
5. **Verify Documents**: Use Verify Document tab (no login required)
6. **Learn More**: Check Use Cases tab for real-world applications

This comprehensive system provides enterprise-grade PKI functionality with an intuitive GUI interface suitable for educational, business, and legal applications.
