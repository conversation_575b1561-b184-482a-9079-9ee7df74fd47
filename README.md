# PKI Document Signer - Registration

A Python GUI application for PKI (Public Key Infrastructure) user registration using Tkinter and the cryptography library.

## Features

- Clean, user-friendly GUI interface
- RSA key pair generation (2048 bits)
- Self-signed certificate creation
- Automatic file organization
- Success notification with fade-in animation
- Input validation

## Requirements

- Python 3.6+
- `cryptography` library
- `tkinter` (usually included with Python)

## Installation

1. Ensure you have Python installed
2. Install the cryptography library if not already available:
   ```bash
   pip install cryptography
   ```

## Usage

1. Run the application:
   ```bash
   python pki_registration.py
   ```

2. Enter a username in the text field
3. Click the "Register" button or press Enter

## What happens during registration:

1. **Key Generation**: Creates a 2048-bit RSA key pair
2. **File Creation**: 
   - Private key saved to `keys/<username>_private.pem`
   - Public key saved to `keys/<username>_public.pem`
   - Self-signed certificate saved to `certs/<username>_cert.pem`
3. **Success Notification**: Shows a popup with fade-in animation

## File Structure

After registration, the following structure is created:

```
project_directory/
├── keys/
│   ├── <username>_private.pem
│   └── <username>_public.pem
├── certs/
│   └── <username>_cert.pem
├── pki_registration.py
└── test_pki_registration.py
```

## Testing

Run the test suite to verify functionality:

```bash
python test_pki_registration.py
```

## Security Notes

- Private keys are stored unencrypted for simplicity
- Self-signed certificates are used (not suitable for production)
- This is a demonstration application for learning purposes

## Username Requirements

- Must contain only letters, numbers, hyphens, and underscores
- Cannot be empty
- Used as the Common Name in the certificate

## Certificate Details

The self-signed certificates include:
- Country: US
- State: State
- City: City
- Organization: PKI Document Signer
- Common Name: <username>
- Validity: 365 days from creation
- Subject Alternative Name: localhost
