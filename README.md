# PKI Document Signer System

A complete Python GUI application suite for PKI (Public Key Infrastructure) user registration and secure login using Tkinter and the cryptography library.

## Applications

### 1. Registration App (`pki_registration.py`)

- Clean, user-friendly GUI interface
- RSA key pair generation (2048 bits)
- Self-signed certificate creation
- Automatic file organization
- Success notification with fade-in animation
- Input validation

### 2. Login App (`pki_login.py`)

- Secure PKI-based authentication
- File selection for private keys and certificates
- Challenge-response authentication protocol
- Signature verification using cryptography library
- Animated success/failure feedback
- Cross-compatibility with registration app output

### 3. Document Signer (`pki_document_signer.py`)

- Document signing with digital signatures
- Support for multiple file types (.txt, .pdf, etc.)
- SHA-256 hashing of document content
- RSA-PSS signature generation
- Organized file storage in signed_docs/ folder
- Animated success feedback

### 4. System Demo (`demo_pki_system.py`)

- Unified interface to launch all applications
- System status monitoring
- User guidance and workflow instructions

### 5. Verification Utility (`verify_signed_document.py`)

- Command-line tool for signature verification
- Certificate information display
- Document integrity validation
- Signed document listing

## Requirements

- Python 3.6+
- `cryptography` library
- `tkinter` (usually included with Python)

## Installation

1. Ensure you have Python installed
2. Install the cryptography library if not already available:
   ```bash
   pip install cryptography
   ```

## Usage

### Quick Start with Demo

```bash
python demo_pki_system.py
```

### Individual Applications

#### Registration

1. Run the registration application:
   ```bash
   python pki_registration.py
   ```
2. Enter a username in the text field
3. Click the "Register" button or press Enter

#### Login

1. Run the login application:
   ```bash
   python pki_login.py
   ```
2. Select your private key file (.pem) from the keys/ folder
3. Select your certificate file (.pem) from the certs/ folder
4. Click "Login" to authenticate

#### Document Signing

1. Run the document signer application:
   ```bash
   python pki_document_signer.py
   ```
2. Select a document to sign (any file type)
3. Select your private key file (.pem) from the keys/ folder
4. Select your certificate file (.pem) from the certs/ folder
5. Click "Sign Document"
6. Check the signed_docs/ folder for results

#### Document Verification

1. List signed documents:
   ```bash
   python verify_signed_document.py --list
   ```
2. Verify a specific document:
   ```bash
   python verify_signed_document.py <document_name>
   ```

## What happens during registration:

1. **Key Generation**: Creates a 2048-bit RSA key pair
2. **File Creation**:
   - Private key saved to `keys/<username>_private.pem`
   - Public key saved to `keys/<username>_public.pem`
   - Self-signed certificate saved to `certs/<username>_cert.pem`
3. **Success Notification**: Shows a popup with fade-in animation

## What happens during login:

1. **File Loading**: Loads the selected private key and certificate files
2. **Public Key Extraction**: Extracts the public key from the certificate
3. **Challenge Generation**: Creates a random 32-character challenge string
4. **Digital Signing**: Signs the challenge using the private key with PSS padding
5. **Signature Verification**: Verifies the signature using the public key
6. **Authentication Result**: Shows success/failure with appropriate animation

## What happens during document signing:

1. **Document Reading**: Reads the selected document file as binary data
2. **Content Hashing**: Creates SHA-256 hash of the document content
3. **Digital Signing**: Signs the hash using the private key with RSA-PSS
4. **File Organization**: Saves files to signed_docs/ folder:
   - Original document (copied)
   - Digital signature (.sig file)
   - Associated certificate (\_cert.pem file)
5. **Success Notification**: Shows animated success message

## File Structure

After using the complete system, the following structure is created:

```
project_directory/
├── keys/
│   ├── <username>_private.pem
│   └── <username>_public.pem
├── certs/
│   └── <username>_cert.pem
├── signed_docs/
│   ├── <document_name>
│   ├── <document_name_without_ext>.sig
│   └── <document_name_without_ext>_cert.pem
├── pki_registration.py
├── pki_login.py
├── pki_document_signer.py
├── demo_pki_system.py
├── verify_signed_document.py
├── sample_document.txt
├── test_pki_registration.py
├── test_pki_login.py
└── test_pki_document_signer.py
```

## Testing

Run the test suites to verify functionality:

### Registration Tests

```bash
python test_pki_registration.py
```

### Login Tests

```bash
python test_pki_login.py
```

### Document Signer Tests

```bash
python test_pki_document_signer.py
```

All test suites include:

- Core functionality testing
- File operations validation
- Cross-compatibility verification
- Error handling tests
- End-to-end workflow validation

## Security Notes

- Private keys are stored unencrypted for simplicity
- Self-signed certificates are used (not suitable for production)
- This is a demonstration application for learning purposes

## Username Requirements

- Must contain only letters, numbers, hyphens, and underscores
- Cannot be empty
- Used as the Common Name in the certificate

## Certificate Details

The self-signed certificates include:

- Country: US
- State: State
- City: City
- Organization: PKI Document Signer
- Common Name: <username>
- Validity: 365 days from creation
- Subject Alternative Name: localhost
