# PKI Document Signer System

A complete Python GUI application suite for PKI (Public Key Infrastructure) user registration and secure login using Tkinter and the cryptography library.

## Applications

### 1. Registration App (`pki_registration.py`)

- Clean, user-friendly GUI interface
- RSA key pair generation (2048 bits)
- Self-signed certificate creation
- Automatic file organization
- Success notification with fade-in animation
- Input validation

### 2. Login App (`pki_login.py`)

- Secure PKI-based authentication
- File selection for private keys and certificates
- Challenge-response authentication protocol
- Signature verification using cryptography library
- Animated success/failure feedback
- Cross-compatibility with registration app output

### 3. System Demo (`demo_pki_system.py`)

- Unified interface to launch both applications
- System status monitoring
- User guidance and workflow instructions

## Requirements

- Python 3.6+
- `cryptography` library
- `tkinter` (usually included with Python)

## Installation

1. Ensure you have Python installed
2. Install the cryptography library if not already available:
   ```bash
   pip install cryptography
   ```

## Usage

### Quick Start with Demo

```bash
python demo_pki_system.py
```

### Individual Applications

#### Registration

1. Run the registration application:
   ```bash
   python pki_registration.py
   ```
2. Enter a username in the text field
3. Click the "Register" button or press Enter

#### Login

1. Run the login application:
   ```bash
   python pki_login.py
   ```
2. Select your private key file (.pem) from the keys/ folder
3. Select your certificate file (.pem) from the certs/ folder
4. Click "Login" to authenticate

## What happens during registration:

1. **Key Generation**: Creates a 2048-bit RSA key pair
2. **File Creation**:
   - Private key saved to `keys/<username>_private.pem`
   - Public key saved to `keys/<username>_public.pem`
   - Self-signed certificate saved to `certs/<username>_cert.pem`
3. **Success Notification**: Shows a popup with fade-in animation

## What happens during login:

1. **File Loading**: Loads the selected private key and certificate files
2. **Public Key Extraction**: Extracts the public key from the certificate
3. **Challenge Generation**: Creates a random 32-character challenge string
4. **Digital Signing**: Signs the challenge using the private key with PSS padding
5. **Signature Verification**: Verifies the signature using the public key
6. **Authentication Result**: Shows success/failure with appropriate animation

## File Structure

After registration, the following structure is created:

```
project_directory/
├── keys/
│   ├── <username>_private.pem
│   └── <username>_public.pem
├── certs/
│   └── <username>_cert.pem
├── pki_registration.py
└── test_pki_registration.py
```

## Testing

Run the test suites to verify functionality:

### Registration Tests

```bash
python test_pki_registration.py
```

### Login Tests

```bash
python test_pki_login.py
```

Both test suites include:

- Core functionality testing
- File operations validation
- Cross-compatibility verification
- Error handling tests

## Security Notes

- Private keys are stored unencrypted for simplicity
- Self-signed certificates are used (not suitable for production)
- This is a demonstration application for learning purposes

## Username Requirements

- Must contain only letters, numbers, hyphens, and underscores
- Cannot be empty
- Used as the Common Name in the certificate

## Certificate Details

The self-signed certificates include:

- Country: US
- State: State
- City: City
- Organization: PKI Document Signer
- Common Name: <username>
- Validity: 365 days from creation
- Subject Alternative Name: localhost
