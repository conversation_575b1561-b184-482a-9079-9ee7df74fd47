#!/usr/bin/env python3
"""
PKI Document Signing System - Complete Integrated Application
All-in-one GUI system for PKI operations: registration, login, signing, verification
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import os
import sys
import hashlib
import base64
import threading
import time
from cryptography.hazmat.primitives import hashes, serialization
from cryptography.hazmat.primitives.asymmetric import rsa, padding
from cryptography.hazmat.primitives.serialization import load_pem_private_key
from cryptography import x509
from cryptography.x509.oid import NameOID
import sqlite3
import json
from datetime import datetime, timedelta, timezone


# Professional Color Scheme
class Colors:
    # Primary Colors
    PRIMARY_DARK = '#1a1a2e'      # Deep navy blue
    PRIMARY_BLUE = '#16213e'      # Dark blue
    ACCENT_BLUE = '#0f3460'       # Medium blue
    LIGHT_BLUE = '#533483'        # Purple-blue

    # Background Colors
    BG_MAIN = '#f8f9fa'           # Light gray background
    BG_CARD = '#ffffff'           # White cards
    BG_HEADER = '#2c3e50'         # Dark header
    BG_SIDEBAR = '#34495e'        # Sidebar

    # Text Colors
    TEXT_PRIMARY = '#2c3e50'      # Dark text
    TEXT_SECONDARY = '#7f8c8d'    # Gray text
    TEXT_LIGHT = '#bdc3c7'        # Light gray text
    TEXT_WHITE = '#ffffff'        # White text

    # Status Colors
    SUCCESS = '#27ae60'           # Green
    WARNING = '#f39c12'           # Orange
    ERROR = '#e74c3c'             # Red
    INFO = '#3498db'              # Blue

    # Interactive Colors
    BUTTON_PRIMARY = '#3498db'    # Primary button
    BUTTON_SUCCESS = '#27ae60'    # Success button
    BUTTON_WARNING = '#f39c12'    # Warning button
    BUTTON_DANGER = '#e74c3c'     # Danger button
    BUTTON_HOVER = '#2980b9'      # Hover state

    # Border Colors
    BORDER_LIGHT = '#ecf0f1'      # Light border
    BORDER_MEDIUM = '#bdc3c7'     # Medium border
    BORDER_DARK = '#95a5a6'       # Dark border


# Professional Fonts
class Fonts:
    # Font families (use single font name for tkinter compatibility)
    PRIMARY = 'Segoe UI'
    SECONDARY = 'Consolas'
    FALLBACK = 'Arial'

    # Font sizes
    TITLE = 24
    HEADING = 18
    SUBHEADING = 14
    BODY = 11
    SMALL = 9
    CAPTION = 8


class DatabaseManager:
    def __init__(self, db_path="pki_system.db"):
        self.db_path = db_path
        self.init_database()
    
    def init_database(self):
        """Initialize database with required tables"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            # Users table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS users (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    username TEXT UNIQUE NOT NULL,
                    private_key_path TEXT NOT NULL,
                    public_key_path TEXT NOT NULL,
                    certificate_path TEXT NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    last_login TIMESTAMP,
                    is_active BOOLEAN DEFAULT 1
                )
            ''')
            
            # Certificates table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS certificates (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id INTEGER,
                    serial_number TEXT UNIQUE NOT NULL,
                    subject_name TEXT NOT NULL,
                    issuer_name TEXT NOT NULL,
                    valid_from TIMESTAMP NOT NULL,
                    valid_until TIMESTAMP NOT NULL,
                    certificate_path TEXT NOT NULL,
                    is_revoked BOOLEAN DEFAULT 0,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (user_id) REFERENCES users (id)
                )
            ''')
            
            # Signed documents table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS signed_documents (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id INTEGER,
                    document_name TEXT NOT NULL,
                    document_path TEXT NOT NULL,
                    signature_path TEXT NOT NULL,
                    document_hash TEXT NOT NULL,
                    signature_algorithm TEXT DEFAULT 'RSA-PSS',
                    hash_algorithm TEXT DEFAULT 'SHA-256',
                    signed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    is_verified BOOLEAN DEFAULT 0,
                    verification_count INTEGER DEFAULT 0,
                    last_verified TIMESTAMP,
                    is_favorite BOOLEAN DEFAULT 0,
                    category TEXT DEFAULT 'General',
                    tags TEXT DEFAULT '',
                    file_size INTEGER DEFAULT 0,
                    original_path TEXT,
                    notes TEXT DEFAULT '',
                    FOREIGN KEY (user_id) REFERENCES users (id)
                )
            ''')
            
            # Verification logs table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS verification_logs (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    document_id INTEGER,
                    verifier_info TEXT,
                    verification_result BOOLEAN NOT NULL,
                    verification_details TEXT,
                    verified_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (document_id) REFERENCES signed_documents (id)
                )
            ''')
            
            # System audit log
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS audit_log (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id INTEGER,
                    action TEXT NOT NULL,
                    details TEXT,
                    ip_address TEXT,
                    user_agent TEXT,
                    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (user_id) REFERENCES users (id)
                )
            ''')

            conn.commit()

            # Migrate existing database if needed
            self.migrate_database()

    def migrate_database(self):
        """Migrate database to add new columns if they don't exist"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()

            # Check if new columns exist in signed_documents table
            cursor.execute("PRAGMA table_info(signed_documents)")
            columns = [column[1] for column in cursor.fetchall()]

            # Add missing columns
            new_columns = [
                ('is_favorite', 'BOOLEAN DEFAULT 0'),
                ('category', 'TEXT DEFAULT "General"'),
                ('tags', 'TEXT DEFAULT ""'),
                ('file_size', 'INTEGER DEFAULT 0'),
                ('original_path', 'TEXT'),
                ('notes', 'TEXT DEFAULT ""')
            ]

            for column_name, column_def in new_columns:
                if column_name not in columns:
                    try:
                        cursor.execute(f'ALTER TABLE signed_documents ADD COLUMN {column_name} {column_def}')
                        print(f"Added column {column_name} to signed_documents table")
                    except sqlite3.OperationalError as e:
                        if "duplicate column name" not in str(e):
                            print(f"Error adding column {column_name}: {e}")

            conn.commit()
    
    def add_user(self, username, private_key_path, public_key_path, certificate_path):
        """Add new user to database"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute('''
                INSERT INTO users (username, private_key_path, public_key_path, certificate_path)
                VALUES (?, ?, ?, ?)
            ''', (username, private_key_path, public_key_path, certificate_path))
            user_id = cursor.lastrowid
            conn.commit()
            return user_id
    
    def get_user_by_username(self, username):
        """Get user information by username"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute('SELECT * FROM users WHERE username = ? AND is_active = 1', (username,))
            return cursor.fetchone()
    
    def update_last_login(self, user_id):
        """Update user's last login timestamp"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute('''
                UPDATE users SET last_login = CURRENT_TIMESTAMP WHERE id = ?
            ''', (user_id,))
            conn.commit()
    
    def add_certificate(self, user_id, serial_number, subject_name, issuer_name, 
                       valid_from, valid_until, certificate_path):
        """Add certificate information to database"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute('''
                INSERT INTO certificates 
                (user_id, serial_number, subject_name, issuer_name, valid_from, valid_until, certificate_path)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ''', (user_id, serial_number, subject_name, issuer_name, valid_from, valid_until, certificate_path))
            conn.commit()
    
    def add_signed_document(self, user_id, document_name, document_path, signature_path, document_hash,
                           category='General', tags='', original_path='', notes=''):
        """Add signed document record to database with enhanced metadata"""
        # Get file size
        file_size = 0
        try:
            if os.path.exists(document_path):
                file_size = os.path.getsize(document_path)
        except:
            pass

        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute('''
                INSERT INTO signed_documents
                (user_id, document_name, document_path, signature_path, document_hash,
                 category, tags, file_size, original_path, notes)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (user_id, document_name, document_path, signature_path, document_hash,
                  category, tags, file_size, original_path, notes))
            document_id = cursor.lastrowid
            conn.commit()
            return document_id
    
    def add_verification_log(self, document_id, verifier_info, verification_result, verification_details):
        """Add verification log entry"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute('''
                INSERT INTO verification_logs 
                (document_id, verifier_info, verification_result, verification_details)
                VALUES (?, ?, ?, ?)
            ''', (document_id, verifier_info, verification_result, verification_details))
            conn.commit()
    
    def log_audit_event(self, user_id, action, details=None):
        """Log audit event"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute('''
                INSERT INTO audit_log (user_id, action, details)
                VALUES (?, ?, ?)
            ''', (user_id, action, details))
            conn.commit()
    
    def get_user_statistics(self):
        """Get system statistics"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()

            # Total users
            cursor.execute('SELECT COUNT(*) FROM users WHERE is_active = 1')
            total_users = cursor.fetchone()[0]

            # Total signed documents
            cursor.execute('SELECT COUNT(*) FROM signed_documents')
            total_documents = cursor.fetchone()[0]

            # Total verifications
            cursor.execute('SELECT COUNT(*) FROM verification_logs')
            total_verifications = cursor.fetchone()[0]

            return {
                'total_users': total_users,
                'total_documents': total_documents,
                'total_verifications': total_verifications
            }

    def get_user_dashboard_data(self, user_id):
        """Get comprehensive dashboard data for a specific user"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()

            dashboard_data = {}

            # User's personal statistics
            cursor.execute('SELECT COUNT(*) FROM signed_documents WHERE user_id = ?', (user_id,))
            dashboard_data['documents_signed'] = cursor.fetchone()[0]

            cursor.execute('SELECT COUNT(*) FROM verification_logs vl JOIN signed_documents sd ON vl.document_id = sd.id WHERE sd.user_id = ?', (user_id,))
            dashboard_data['documents_verified'] = cursor.fetchone()[0]

            # Recent activity (last 10 documents)
            cursor.execute('''
                SELECT document_name, signed_at, verification_count
                FROM signed_documents
                WHERE user_id = ?
                ORDER BY signed_at DESC
                LIMIT 10
            ''', (user_id,))
            dashboard_data['recent_documents'] = cursor.fetchall()

            # Monthly signing activity (last 12 months)
            cursor.execute('''
                SELECT strftime('%Y-%m', signed_at) as month, COUNT(*) as count
                FROM signed_documents
                WHERE user_id = ? AND signed_at >= date('now', '-12 months')
                GROUP BY strftime('%Y-%m', signed_at)
                ORDER BY month DESC
            ''', (user_id,))
            dashboard_data['monthly_activity'] = cursor.fetchall()

            # Document verification statistics
            cursor.execute('''
                SELECT
                    SUM(verification_count) as total_verifications,
                    AVG(verification_count) as avg_verifications,
                    MAX(verification_count) as max_verifications
                FROM signed_documents
                WHERE user_id = ?
            ''', (user_id,))
            verification_stats = cursor.fetchone()
            dashboard_data['verification_stats'] = {
                'total': verification_stats[0] or 0,
                'average': round(verification_stats[1] or 0, 2),
                'max': verification_stats[2] or 0
            }

            # Certificate information
            cursor.execute('''
                SELECT serial_number, subject_name, valid_from, valid_until, is_revoked
                FROM certificates
                WHERE user_id = ? AND is_revoked = 0
                ORDER BY created_at DESC
                LIMIT 1
            ''', (user_id,))
            cert_info = cursor.fetchone()
            if cert_info:
                dashboard_data['certificate'] = {
                    'serial_number': cert_info[0],
                    'subject_name': cert_info[1],
                    'valid_from': cert_info[2],
                    'valid_until': cert_info[3],
                    'is_revoked': cert_info[4]
                }

            # Recent audit activity
            cursor.execute('''
                SELECT action, details, timestamp
                FROM audit_log
                WHERE user_id = ?
                ORDER BY timestamp DESC
                LIMIT 5
            ''', (user_id,))
            dashboard_data['recent_activity'] = cursor.fetchall()

            return dashboard_data

    def get_recent_documents(self, user_id, limit=10):
        """Get recent documents for quick access"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute('''
                SELECT id, document_name, document_path, signed_at, verification_count, category, is_favorite
                FROM signed_documents
                WHERE user_id = ?
                ORDER BY signed_at DESC
                LIMIT ?
            ''', (user_id, limit))
            return cursor.fetchall()

    def get_favorite_documents(self, user_id):
        """Get user's favorite documents"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute('''
                SELECT id, document_name, document_path, signed_at, verification_count, category
                FROM signed_documents
                WHERE user_id = ? AND is_favorite = 1
                ORDER BY signed_at DESC
            ''', (user_id,))
            return cursor.fetchall()

    def toggle_document_favorite(self, document_id):
        """Toggle favorite status of a document"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute('''
                UPDATE signed_documents
                SET is_favorite = CASE WHEN is_favorite = 1 THEN 0 ELSE 1 END
                WHERE id = ?
            ''', (document_id,))
            conn.commit()

    def get_document_categories(self, user_id):
        """Get all categories used by a user"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute('''
                SELECT DISTINCT category, COUNT(*) as count
                FROM signed_documents
                WHERE user_id = ?
                GROUP BY category
                ORDER BY count DESC
            ''', (user_id,))
            return cursor.fetchall()

    def get_documents_by_category(self, user_id, category):
        """Get documents by category"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute('''
                SELECT id, document_name, document_path, signed_at, verification_count, is_favorite
                FROM signed_documents
                WHERE user_id = ? AND category = ?
                ORDER BY signed_at DESC
            ''', (user_id, category))
            return cursor.fetchall()

    def update_document_metadata(self, document_id, category=None, tags=None, notes=None):
        """Update document metadata"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            updates = []
            params = []

            if category is not None:
                updates.append("category = ?")
                params.append(category)
            if tags is not None:
                updates.append("tags = ?")
                params.append(tags)
            if notes is not None:
                updates.append("notes = ?")
                params.append(notes)

            if updates:
                params.append(document_id)
                cursor.execute(f'''
                    UPDATE signed_documents
                    SET {", ".join(updates)}
                    WHERE id = ?
                ''', params)
                conn.commit()

    def search_documents(self, user_id, search_term):
        """Search documents by name, category, tags, or notes"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute('''
                SELECT id, document_name, document_path, signed_at, verification_count, category, is_favorite
                FROM signed_documents
                WHERE user_id = ? AND (
                    document_name LIKE ? OR
                    category LIKE ? OR
                    tags LIKE ? OR
                    notes LIKE ?
                )
                ORDER BY signed_at DESC
            ''', (user_id, f'%{search_term}%', f'%{search_term}%', f'%{search_term}%', f'%{search_term}%'))
            return cursor.fetchall()
    
    def get_user_documents(self, user_id):
        """Get all documents signed by a user"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute('''
                SELECT document_name, document_path, signature_path, signed_at, verification_count
                FROM signed_documents
                WHERE user_id = ?
                ORDER BY signed_at DESC
            ''', (user_id,))
            return cursor.fetchall()

    def find_document_by_hash(self, document_hash):
        """Find document ID by hash"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute('SELECT id FROM signed_documents WHERE document_hash = ?', (document_hash,))
            result = cursor.fetchone()
            return result[0] if result else None

    def update_document_verification(self, document_id):
        """Update document verification count and timestamp"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute('''
                UPDATE signed_documents
                SET verification_count = verification_count + 1,
                    last_verified = CURRENT_TIMESTAMP,
                    is_verified = 1
                WHERE id = ?
            ''', (document_id,))
            conn.commit()

    def get_document_verification_history(self, document_id):
        """Get verification history for a document"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute('''
                SELECT verifier_info, verification_result, verification_details, verified_at
                FROM verification_logs
                WHERE document_id = ?
                ORDER BY verified_at DESC
            ''', (document_id,))
            return cursor.fetchall()

    def get_system_audit_log(self, limit=100):
        """Get recent system audit log entries"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute('''
                SELECT u.username, a.action, a.details, a.timestamp
                FROM audit_log a
                LEFT JOIN users u ON a.user_id = u.id
                ORDER BY a.timestamp DESC
                LIMIT ?
            ''', (limit,))
            return cursor.fetchall()

    def get_all_tables_data(self):
        """Get data from all tables for database viewer"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()

            tables_data = {}

            # Get users data
            cursor.execute('SELECT * FROM users')
            tables_data['users'] = {
                'columns': ['id', 'username', 'private_key_path', 'public_key_path', 'certificate_path', 'created_at', 'last_login', 'is_active'],
                'data': cursor.fetchall()
            }

            # Get certificates data
            cursor.execute('SELECT * FROM certificates')
            tables_data['certificates'] = {
                'columns': ['id', 'user_id', 'serial_number', 'subject_name', 'issuer_name', 'valid_from', 'valid_until', 'certificate_path', 'is_revoked', 'created_at'],
                'data': cursor.fetchall()
            }

            # Get signed documents data
            cursor.execute('SELECT * FROM signed_documents')
            tables_data['signed_documents'] = {
                'columns': ['id', 'user_id', 'document_name', 'document_path', 'signature_path', 'document_hash', 'signature_algorithm', 'hash_algorithm', 'signed_at', 'is_verified', 'verification_count', 'last_verified'],
                'data': cursor.fetchall()
            }

            # Get verification logs data
            cursor.execute('SELECT * FROM verification_logs')
            tables_data['verification_logs'] = {
                'columns': ['id', 'document_id', 'verifier_info', 'verification_result', 'verification_details', 'verified_at'],
                'data': cursor.fetchall()
            }

            # Get audit log data
            cursor.execute('SELECT * FROM audit_log')
            tables_data['audit_log'] = {
                'columns': ['id', 'user_id', 'action', 'details', 'ip_address', 'user_agent', 'timestamp'],
                'data': cursor.fetchall()
            }

            return tables_data

    def get_certificate_info(self, user_id):
        """Get certificate information for a user"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute('''
                SELECT serial_number, subject_name, valid_from, valid_until, is_revoked
                FROM certificates
                WHERE user_id = ? AND is_revoked = 0
                ORDER BY created_at DESC
                LIMIT 1
            ''', (user_id,))
            return cursor.fetchone()

    def revoke_certificate(self, user_id, reason="User requested"):
        """Revoke a user's certificate"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute('''
                UPDATE certificates
                SET is_revoked = 1
                WHERE user_id = ? AND is_revoked = 0
            ''', (user_id,))
            conn.commit()

            # Log revocation
            cursor.execute('''
                INSERT INTO audit_log (user_id, action, details)
                VALUES (?, ?, ?)
            ''', (user_id, "CERTIFICATE_REVOKED", reason))
            conn.commit()


class PKISystem:
    def __init__(self, root):
        self.root = root
        self.current_user = None
        self.current_user_id = None
        self.is_logged_in = False
        self.private_key_path = None
        self.certificate_path = None
        
        # Initialize database
        self.db = DatabaseManager()
        
        # Ensure directories exist
        self.ensure_directories()
        
        # Setup UI
        self.setup_ui()
        
        # Update status
        self.update_system_status()
    
    def ensure_directories(self):
        """Create necessary directories if they don't exist"""
        directories = ['keys', 'certs', 'signed_docs']
        for directory in directories:
            if not os.path.exists(directory):
                os.makedirs(directory)
    
    def setup_ui(self):
        """Setup the main interface with professional styling and responsive design"""
        # Window configuration with responsive sizing
        self.root.title("PKI Document Signing System - Professional Edition")
        self.root.geometry("1400x900")
        self.root.minsize(800, 600)  # Reduced minimum size for better responsiveness
        self.root.configure(bg=Colors.BG_MAIN)

        # Configure window icon and styling
        try:
            self.root.state('zoomed')  # Maximize on Windows
        except:
            pass

        # Configure modern styling
        self.configure_styles()

        # Create main container with scrollable content
        self.create_main_container()

        # Create header
        self.create_header()

        # Create main content area
        self.create_main_content()

        # Create status bar
        self.create_status_bar()

        # Add smooth animations
        self.setup_animations()

        # Setup responsive behavior
        self.setup_responsive_design()

    def setup_responsive_design(self):
        """Setup responsive design behaviors"""
        # Bind window resize events
        self.root.bind('<Configure>', self.on_window_resize)

        # Store initial window size for responsive calculations
        self.root.update_idletasks()
        self.initial_width = self.root.winfo_width()
        self.initial_height = self.root.winfo_height()

    def on_window_resize(self, event):
        """Handle window resize events for responsive design"""
        # Only handle main window resize events
        if event.widget == self.root:
            current_width = event.width
            current_height = event.height

            # Adjust component sizes based on window size
            self.adjust_responsive_layout(current_width, current_height)

    def adjust_responsive_layout(self, width, height):
        """Adjust layout based on window size"""
        # Calculate responsive padding based on window width
        if width < 1000:
            # Small screen - reduce padding
            section_padx = 10
            content_padx = 15
            header_padx = 10
        elif width < 1300:
            # Medium screen - moderate padding
            section_padx = 15
            content_padx = 20
            header_padx = 15
        else:
            # Large screen - full padding
            section_padx = 20
            content_padx = 25
            header_padx = 20

        # Update section padding dynamically
        try:
            # Update content frame padding
            if hasattr(self, 'content_frame'):
                self.content_frame.pack_configure(padx=content_padx)
        except:
            pass

    def configure_styles(self):
        """Configure modern TTK styles"""
        style = ttk.Style()

        # Configure modern button style
        style.configure('Modern.TButton',
                       font=(Fonts.PRIMARY, Fonts.BODY, 'bold'),
                       padding=(20, 10))

        # Configure modern frame style
        style.configure('Card.TFrame',
                       background=Colors.BG_CARD,
                       relief='flat',
                       borderwidth=1)

    def setup_animations(self):
        """Setup smooth animations and transitions"""
        # Add fade-in effect for the main window
        self.root.attributes('-alpha', 0.0)
        self.fade_in_window()

    def fade_in_window(self):
        """Smooth fade-in animation for the main window"""
        alpha = self.root.attributes('-alpha')
        if alpha < 1.0:
            alpha += 0.05
            self.root.attributes('-alpha', alpha)
            self.root.after(20, self.fade_in_window)
    
    def create_main_container(self):
        """Create modern scrollable main container"""
        # Main frame with modern background
        self.main_frame = tk.Frame(self.root, bg=Colors.BG_MAIN)
        self.main_frame.pack(fill='both', expand=True, padx=2, pady=2)

        # Create modern canvas with custom styling
        self.canvas = tk.Canvas(self.main_frame,
                               bg=Colors.BG_MAIN,
                               highlightthickness=0,
                               bd=0)

        # Custom styled scrollbar
        self.scrollbar = tk.Scrollbar(self.main_frame,
                                     orient="vertical",
                                     command=self.canvas.yview,
                                     bg=Colors.BG_SIDEBAR,
                                     troughcolor=Colors.BG_MAIN,
                                     activebackground=Colors.BUTTON_HOVER,
                                     width=12)

        self.scrollable_frame = tk.Frame(self.canvas, bg=Colors.BG_MAIN)

        # Configure scrolling
        self.scrollable_frame.bind(
            "<Configure>",
            lambda e: self.canvas.configure(scrollregion=self.canvas.bbox("all"))
        )

        self.canvas_window = self.canvas.create_window((0, 0), window=self.scrollable_frame, anchor="nw")
        self.canvas.configure(yscrollcommand=self.scrollbar.set)

        # Bind canvas resize event
        self.canvas.bind("<Configure>", self._on_canvas_configure)

        # Pack with modern layout
        self.canvas.pack(side="left", fill="both", expand=True)
        self.scrollbar.pack(side="right", fill="y")

        # Enhanced mouse wheel binding
        self.canvas.bind("<MouseWheel>", self._on_mousewheel)
        self.root.bind("<MouseWheel>", self._on_mousewheel)
    
    def _on_mousewheel(self, event):
        """Handle mouse wheel scrolling"""
        self.canvas.yview_scroll(int(-1*(event.delta/120)), "units")

    def _on_canvas_configure(self, event):
        """Handle canvas resize to update scroll region and width"""
        # Update the canvas scroll region when the frame changes size
        self.canvas.configure(scrollregion=self.canvas.bbox("all"))

        # Update the canvas window width to match the canvas width
        canvas_width = event.width
        self.canvas.itemconfig(self.canvas_window, width=canvas_width)
    
    def create_header(self):
        """Create modern responsive professional header"""
        # Get responsive sizing
        window_width = self.root.winfo_width() if self.root.winfo_width() > 1 else 1400

        if window_width < 1000:
            header_height = 80
            header_padx = 10
            icon_size = 24
            title_size = Fonts.HEADING
        elif window_width < 1300:
            header_height = 90
            header_padx = 15
            icon_size = 28
            title_size = Fonts.TITLE - 2
        else:
            header_height = 100
            header_padx = 20
            icon_size = 32
            title_size = Fonts.TITLE

        # Main header container with gradient effect
        header_container = tk.Frame(self.scrollable_frame, bg=Colors.BG_MAIN)
        header_container.pack(fill='x', padx=0, pady=(20, 10))

        # Header card with shadow effect - responsive height
        header_frame = tk.Frame(header_container, bg=Colors.PRIMARY_DARK,
                               relief='flat', bd=0, height=header_height)
        header_frame.pack(fill='x', padx=header_padx)
        header_frame.pack_propagate(False)

        # Add subtle shadow effect
        shadow_frame = tk.Frame(header_container, bg=Colors.BORDER_MEDIUM, height=2)
        shadow_frame.pack(fill='x')

        # Left side - Modern title section
        left_frame = tk.Frame(header_frame, bg=Colors.PRIMARY_DARK)
        left_frame.pack(side='left', fill='y', padx=30, pady=15)

        # Icon and title container
        title_container = tk.Frame(left_frame, bg=Colors.PRIMARY_DARK)
        title_container.pack(anchor='w')

        # Responsive icon
        icon_label = tk.Label(title_container, text="🔐",
                             font=(Fonts.PRIMARY, icon_size),
                             fg=Colors.TEXT_WHITE, bg=Colors.PRIMARY_DARK)
        icon_label.pack(side='left', padx=(0, 15))

        # Title and subtitle
        text_container = tk.Frame(title_container, bg=Colors.PRIMARY_DARK)
        text_container.pack(side='left', fill='y')

        # Responsive title
        title_text = "PKI Document Signing System" if window_width >= 1000 else "PKI System"
        title_label = tk.Label(text_container, text=title_text,
                              font=(Fonts.PRIMARY, title_size, 'bold'),
                              fg=Colors.TEXT_WHITE, bg=Colors.PRIMARY_DARK)
        title_label.pack(anchor='w')

        # Responsive subtitle
        if window_width >= 1000:
            subtitle_text = "Professional Digital Authentication Platform"
            subtitle_label = tk.Label(text_container, text=subtitle_text,
                                     font=(Fonts.PRIMARY, Fonts.BODY),
                                     fg=Colors.TEXT_LIGHT, bg=Colors.PRIMARY_DARK)
            subtitle_label.pack(anchor='w')

        # Right side - Modern user status
        right_frame = tk.Frame(header_frame, bg=Colors.PRIMARY_DARK)
        right_frame.pack(side='right', fill='y', padx=30, pady=15)

        # Status container
        status_container = tk.Frame(right_frame, bg=Colors.PRIMARY_DARK)
        status_container.pack(anchor='e')

        self.user_status_label = tk.Label(status_container, text="● Not Logged In",
                                         font=(Fonts.PRIMARY, Fonts.SUBHEADING, 'bold'),
                                         fg=Colors.ERROR, bg=Colors.PRIMARY_DARK)
        self.user_status_label.pack(anchor='e')

        self.user_info_label = tk.Label(status_container, text="Please login to access all features",
                                       font=(Fonts.PRIMARY, Fonts.SMALL),
                                       fg=Colors.TEXT_LIGHT, bg=Colors.PRIMARY_DARK)
        self.user_info_label.pack(anchor='e')
    
    def create_main_content(self):
        """Create the main content area with dynamic sections based on login state"""
        self.content_frame = tk.Frame(self.scrollable_frame, bg='#f0f0f0')
        self.content_frame.pack(fill='both', expand=True, padx=0, pady=10)

        # Create initial UI (before login)
        self.create_initial_ui()

    def create_initial_ui(self):
        """Create the initial UI before login"""
        # Clear existing content
        for widget in self.content_frame.winfo_children():
            widget.destroy()

        # Create sections for non-authenticated users
        self.create_registration_section(self.content_frame)
        self.create_login_section(self.content_frame)
        self.create_verification_section(self.content_frame)

    def create_user_ui(self):
        """Create modern, user-friendly interface after login"""
        # Clear existing content
        for widget in self.content_frame.winfo_children():
            widget.destroy()

        # Create modern user interface
        self.create_modern_user_interface()

    def create_modern_user_interface(self):
        """Create a modern, intuitive user interface"""
        # Main container with sidebar and content
        main_container = tk.Frame(self.content_frame, bg=Colors.BG_MAIN)
        main_container.pack(fill='both', expand=True)

        # Left sidebar for navigation
        self.create_navigation_sidebar(main_container)

        # Right content area
        self.create_content_area(main_container)

        # Initialize with dashboard
        self.show_dashboard()

    def create_navigation_sidebar(self, parent):
        """Create modern navigation sidebar"""
        # Sidebar frame
        self.sidebar = tk.Frame(parent, bg=Colors.BG_SIDEBAR, width=280)
        self.sidebar.pack(side='left', fill='y', padx=(0, 1))
        self.sidebar.pack_propagate(False)

        # User profile section
        profile_frame = tk.Frame(self.sidebar, bg=Colors.BG_SIDEBAR)
        profile_frame.pack(fill='x', pady=20, padx=20)

        # User avatar (placeholder)
        avatar_frame = tk.Frame(profile_frame, bg=Colors.BUTTON_PRIMARY, width=60, height=60)
        avatar_frame.pack()
        avatar_frame.pack_propagate(False)

        avatar_label = tk.Label(avatar_frame, text="👤",
                               font=(Fonts.PRIMARY, 24),
                               fg=Colors.TEXT_WHITE, bg=Colors.BUTTON_PRIMARY)
        avatar_label.pack(expand=True)

        # User name
        user_name_label = tk.Label(profile_frame, text=f"Welcome, {self.current_user}!",
                                  font=(Fonts.PRIMARY, Fonts.HEADING, 'bold'),
                                  fg=Colors.TEXT_PRIMARY, bg=Colors.BG_SIDEBAR)
        user_name_label.pack(pady=(10, 5))

        # User status
        status_label = tk.Label(profile_frame, text="● Online",
                               font=(Fonts.PRIMARY, Fonts.SMALL),
                               fg=Colors.SUCCESS, bg=Colors.BG_SIDEBAR)
        status_label.pack()

        # Navigation menu
        self.create_navigation_menu()

    def create_navigation_menu(self):
        """Create navigation menu with modern buttons"""
        # Menu container
        menu_frame = tk.Frame(self.sidebar, bg=Colors.BG_SIDEBAR)
        menu_frame.pack(fill='both', expand=True, padx=20, pady=20)

        # Menu items
        menu_items = [
            ("📊", "Dashboard", "dashboard", Colors.BUTTON_PRIMARY),
            ("📄", "Documents", "documents", Colors.BUTTON_SUCCESS),
            ("✍️", "Sign Document", "sign", Colors.WARNING),
            ("✅", "Verify Document", "verify", Colors.INFO),
            ("📈", "Analytics", "analytics", Colors.LIGHT_BLUE),
            ("⚙️", "Settings", "settings", Colors.BUTTON_DANGER)
        ]

        self.nav_buttons = {}
        for icon, text, action, color in menu_items:
            btn = self.create_nav_button(menu_frame, icon, text, action, color)
            btn.pack(fill='x', pady=5)
            self.nav_buttons[action] = btn

        # Logout section at bottom - more prominent
        logout_section = tk.Frame(self.sidebar, bg=Colors.BG_SIDEBAR)
        logout_section.pack(side='bottom', fill='x', padx=20, pady=20)

        # Separator line
        separator = tk.Frame(logout_section, bg=Colors.BORDER_MEDIUM, height=1)
        separator.pack(fill='x', pady=(0, 15))

        # Logout button - more prominent
        logout_btn = tk.Button(logout_section,
                              text="🚪  Logout",
                              font=(Fonts.PRIMARY, Fonts.BODY, 'bold'),
                              fg=Colors.TEXT_WHITE,
                              bg=Colors.ERROR,
                              activebackground=Colors.BUTTON_DANGER,
                              activeforeground=Colors.TEXT_WHITE,
                              relief='flat',
                              bd=0,
                              padx=20,
                              pady=12,
                              cursor='hand2',
                              command=self.perform_logout)
        logout_btn.pack(fill='x')

        # Hover effects for logout button
        def on_logout_enter(e):
            logout_btn.config(bg=Colors.BUTTON_DANGER)

        def on_logout_leave(e):
            logout_btn.config(bg=Colors.ERROR)

        logout_btn.bind("<Enter>", on_logout_enter)
        logout_btn.bind("<Leave>", on_logout_leave)

    def create_nav_button(self, parent, icon, text, action, color):
        """Create a modern navigation button"""
        btn_frame = tk.Frame(parent, bg=Colors.BG_SIDEBAR)

        btn = tk.Button(btn_frame,
                       text=f"{icon}  {text}",
                       font=(Fonts.PRIMARY, Fonts.BODY, 'bold'),
                       fg=Colors.TEXT_PRIMARY,
                       bg=Colors.BG_CARD,
                       activebackground=color,
                       activeforeground=Colors.TEXT_WHITE,
                       relief='flat',
                       bd=0,
                       padx=20,
                       pady=15,
                       cursor='hand2',
                       anchor='w',
                       command=lambda: self.handle_navigation(action))
        btn.pack(fill='x')

        # Hover effects
        def on_enter(e):
            btn.config(bg=color, fg=Colors.TEXT_WHITE)

        def on_leave(e):
            if action != getattr(self, 'current_view', 'dashboard'):
                btn.config(bg=Colors.BG_CARD, fg=Colors.TEXT_PRIMARY)

        btn.bind("<Enter>", on_enter)
        btn.bind("<Leave>", on_leave)

        return btn_frame

    def create_content_area(self, parent):
        """Create main content area"""
        # Content container
        self.content_area = tk.Frame(parent, bg=Colors.BG_MAIN)
        self.content_area.pack(side='right', fill='both', expand=True)

        # Content header
        self.content_header = tk.Frame(self.content_area, bg=Colors.BG_CARD, height=80)
        self.content_header.pack(fill='x', padx=20, pady=(20, 10))
        self.content_header.pack_propagate(False)

        # Content body (scrollable)
        self.create_scrollable_content()

    def create_scrollable_content(self):
        """Create scrollable content area"""
        # Canvas for scrolling
        self.content_canvas = tk.Canvas(self.content_area, bg=Colors.BG_MAIN, highlightthickness=0)
        self.content_scrollbar = ttk.Scrollbar(self.content_area, orient="vertical", command=self.content_canvas.yview)

        # Scrollable frame
        self.scrollable_content = tk.Frame(self.content_canvas, bg=Colors.BG_MAIN)

        # Configure scrolling
        self.scrollable_content.bind(
            "<Configure>",
            lambda e: self.content_canvas.configure(scrollregion=self.content_canvas.bbox("all"))
        )

        self.content_canvas.create_window((0, 0), window=self.scrollable_content, anchor="nw")
        self.content_canvas.configure(yscrollcommand=self.content_scrollbar.set)

        # Pack canvas and scrollbar
        self.content_canvas.pack(side="left", fill="both", expand=True, padx=(20, 0), pady=(0, 20))
        self.content_scrollbar.pack(side="right", fill="y", padx=(0, 20), pady=(0, 20))

        # Mouse wheel scrolling
        def _on_mousewheel(event):
            self.content_canvas.yview_scroll(int(-1*(event.delta/120)), "units")

        self.content_canvas.bind("<MouseWheel>", _on_mousewheel)

    def handle_navigation(self, action):
        """Handle navigation menu clicks"""
        # Update current view
        self.current_view = action

        # Update navigation button states
        self.update_nav_button_states()

        # Clear content area
        for widget in self.scrollable_content.winfo_children():
            widget.destroy()

        # Update content header
        self.update_content_header(action)

        # Show appropriate content
        if action == 'dashboard':
            self.show_dashboard()
        elif action == 'documents':
            self.show_documents_view()
        elif action == 'sign':
            self.show_sign_view()
        elif action == 'verify':
            self.show_verify_view()
        elif action == 'analytics':
            self.show_analytics_view()
        elif action == 'settings':
            self.show_settings_view()
        elif action == 'logout':
            self.perform_logout()

    def update_nav_button_states(self):
        """Update navigation button visual states"""
        for action, btn_frame in self.nav_buttons.items():
            btn = btn_frame.winfo_children()[0]  # Get the button widget
            if action == self.current_view:
                # Active state
                if action == 'dashboard':
                    color = Colors.BUTTON_PRIMARY
                elif action == 'documents':
                    color = Colors.BUTTON_SUCCESS
                elif action == 'sign':
                    color = Colors.WARNING
                elif action == 'verify':
                    color = Colors.INFO
                elif action == 'analytics':
                    color = Colors.LIGHT_BLUE
                else:
                    color = Colors.BUTTON_DANGER

                btn.config(bg=color, fg=Colors.TEXT_WHITE)
            else:
                # Inactive state
                btn.config(bg=Colors.BG_CARD, fg=Colors.TEXT_PRIMARY)

    def update_content_header(self, action):
        """Update content header based on current view"""
        # Clear header
        for widget in self.content_header.winfo_children():
            widget.destroy()

        # Header content based on action
        headers = {
            'dashboard': ("📊 Dashboard", "Overview of your PKI activities"),
            'documents': ("📄 Document Management", "Manage and organize your signed documents"),
            'sign': ("✍️ Sign Document", "Digitally sign a new document"),
            'verify': ("✅ Verify Document", "Verify document signatures"),
            'analytics': ("📈 Analytics", "Detailed analytics and reports"),
            'settings': ("⚙️ Settings", "System settings and preferences")
        }

        title, subtitle = headers.get(action, ("PKI System", "Professional document signing"))

        # Title
        title_label = tk.Label(self.content_header, text=title,
                              font=(Fonts.PRIMARY, Fonts.TITLE, 'bold'),
                              fg=Colors.TEXT_PRIMARY, bg=Colors.BG_CARD)
        title_label.pack(side='left', padx=20, pady=20)

        # Subtitle
        subtitle_label = tk.Label(self.content_header, text=subtitle,
                                 font=(Fonts.PRIMARY, Fonts.BODY),
                                 fg=Colors.TEXT_SECONDARY, bg=Colors.BG_CARD)
        subtitle_label.pack(side='left', padx=(0, 20), pady=20)

        # Header action buttons
        btn_frame = tk.Frame(self.content_header, bg=Colors.BG_CARD)
        btn_frame.pack(side='right', padx=20, pady=20)

        # User info and logout in header
        user_info_frame = tk.Frame(btn_frame, bg=Colors.BG_CARD)
        user_info_frame.pack(side='right')

        # User name
        user_label = tk.Label(user_info_frame, text=f"👤 {self.current_user}",
                             font=(Fonts.PRIMARY, Fonts.SMALL, 'bold'),
                             fg=Colors.TEXT_PRIMARY, bg=Colors.BG_CARD)
        user_label.pack(side='left', padx=(0, 15))

        # Quick logout button in header
        header_logout_btn = tk.Button(user_info_frame,
                                     text="🚪 Logout",
                                     font=(Fonts.PRIMARY, Fonts.SMALL, 'bold'),
                                     fg=Colors.TEXT_WHITE,
                                     bg=Colors.ERROR,
                                     activebackground=Colors.BUTTON_DANGER,
                                     activeforeground=Colors.TEXT_WHITE,
                                     relief='flat',
                                     bd=0,
                                     padx=15,
                                     pady=8,
                                     cursor='hand2',
                                     command=self.perform_logout)
        header_logout_btn.pack(side='right')

        # Hover effects for header logout
        def on_header_logout_enter(e):
            header_logout_btn.config(bg=Colors.BUTTON_DANGER)

        def on_header_logout_leave(e):
            header_logout_btn.config(bg=Colors.ERROR)

        header_logout_btn.bind("<Enter>", on_header_logout_enter)
        header_logout_btn.bind("<Leave>", on_header_logout_leave)

        # Context-specific action buttons
        if action in ['documents', 'analytics']:
            context_btn_frame = tk.Frame(btn_frame, bg=Colors.BG_CARD)
            context_btn_frame.pack(side='left', padx=(0, 20))

            if action == 'documents':
                refresh_btn = self.create_modern_button(context_btn_frame, "🔄 Refresh",
                                                       self.refresh_documents, Colors.BUTTON_PRIMARY)
                refresh_btn.pack(side='right', padx=5)

            elif action == 'analytics':
                export_btn = self.create_modern_button(context_btn_frame, "📊 Export",
                                                      self.export_analytics, Colors.SUCCESS)
                export_btn.pack(side='right', padx=5)

    def show_dashboard(self):
        """Show modern dashboard view"""
        # Dashboard container
        dashboard_container = tk.Frame(self.scrollable_content, bg=Colors.BG_MAIN)
        dashboard_container.pack(fill='both', expand=True, padx=20, pady=20)

        # Welcome message
        welcome_frame = tk.Frame(dashboard_container, bg=Colors.BG_CARD, relief='flat', bd=1)
        welcome_frame.pack(fill='x', pady=(0, 20))

        welcome_content = tk.Frame(welcome_frame, bg=Colors.BG_CARD)
        welcome_content.pack(fill='x', padx=30, pady=20)

        welcome_label = tk.Label(welcome_content,
                                text=f"Welcome back, {self.current_user}! 👋",
                                font=(Fonts.PRIMARY, Fonts.TITLE, 'bold'),
                                fg=Colors.TEXT_PRIMARY, bg=Colors.BG_CARD)
        welcome_label.pack(anchor='w')

        welcome_desc = tk.Label(welcome_content,
                               text="Here's an overview of your PKI activities and system status.",
                               font=(Fonts.PRIMARY, Fonts.BODY),
                               fg=Colors.TEXT_SECONDARY, bg=Colors.BG_CARD)
        welcome_desc.pack(anchor='w', pady=(5, 0))

        # Quick stats cards
        self.create_dashboard_stats(dashboard_container)

        # Recent activity
        self.create_dashboard_activity(dashboard_container)

        # Quick actions
        self.create_dashboard_quick_actions(dashboard_container)

    def create_dashboard_stats(self, parent):
        """Create dashboard statistics cards"""
        stats_frame = tk.Frame(parent, bg=Colors.BG_MAIN)
        stats_frame.pack(fill='x', pady=(0, 20))

        # Get user stats
        dashboard_data = self.db.get_user_dashboard_data(self.current_user_id)

        # Stats data
        stats = [
            {
                'title': 'Documents Signed',
                'value': dashboard_data.get('documents_signed', 0),
                'icon': '📄',
                'color': Colors.BUTTON_SUCCESS,
                'bg': '#f0f9ff'
            },
            {
                'title': 'Total Verifications',
                'value': dashboard_data.get('verification_stats', {}).get('total', 0),
                'icon': '✅',
                'color': Colors.INFO,
                'bg': '#f0fdf4'
            },
            {
                'title': 'Average Verifications',
                'value': dashboard_data.get('verification_stats', {}).get('average', 0),
                'icon': '📊',
                'color': Colors.WARNING,
                'bg': '#fffbeb'
            },
            {
                'title': 'Documents Verified',
                'value': dashboard_data.get('documents_verified', 0),
                'icon': '🔍',
                'color': Colors.LIGHT_BLUE,
                'bg': '#fef2f2'
            }
        ]

        # Create grid of stat cards
        for i, stat in enumerate(stats):
            self.create_stat_card_modern(stats_frame, stat, i)

    def create_stat_card_modern(self, parent, stat, position):
        """Create modern statistics card"""
        # Card container
        card = tk.Frame(parent, bg=Colors.BG_CARD, relief='flat', bd=1)
        card.grid(row=0, column=position, padx=10, pady=10, sticky='ew')

        # Configure grid
        parent.grid_columnconfigure(position, weight=1)

        # Card content
        content = tk.Frame(card, bg=Colors.BG_CARD)
        content.pack(fill='both', expand=True, padx=20, pady=20)

        # Icon and value row
        top_row = tk.Frame(content, bg=Colors.BG_CARD)
        top_row.pack(fill='x')

        # Icon
        icon_label = tk.Label(top_row, text=stat['icon'],
                             font=(Fonts.PRIMARY, 24),
                             fg=stat['color'], bg=Colors.BG_CARD)
        icon_label.pack(side='left')

        # Value
        value_label = tk.Label(top_row, text=str(stat['value']),
                              font=(Fonts.PRIMARY, Fonts.TITLE, 'bold'),
                              fg=stat['color'], bg=Colors.BG_CARD)
        value_label.pack(side='right')

        # Title
        title_label = tk.Label(content, text=stat['title'],
                              font=(Fonts.PRIMARY, Fonts.BODY),
                              fg=Colors.TEXT_SECONDARY, bg=Colors.BG_CARD)
        title_label.pack(anchor='w', pady=(10, 0))

    def create_dashboard_activity(self, parent):
        """Create recent activity section"""
        activity_frame = tk.Frame(parent, bg=Colors.BG_CARD, relief='flat', bd=1)
        activity_frame.pack(fill='x', pady=(0, 20))

        # Header
        header_frame = tk.Frame(activity_frame, bg=Colors.BG_CARD)
        header_frame.pack(fill='x', padx=30, pady=(20, 10))

        tk.Label(header_frame, text="📋 Recent Activity",
                font=(Fonts.PRIMARY, Fonts.HEADING, 'bold'),
                fg=Colors.TEXT_PRIMARY, bg=Colors.BG_CARD).pack(side='left')

        # Activity list
        activity_content = tk.Frame(activity_frame, bg=Colors.BG_CARD)
        activity_content.pack(fill='x', padx=30, pady=(0, 20))

        # Get recent documents
        recent_docs = self.db.get_recent_documents(self.current_user_id, 5)

        if recent_docs:
            for doc_id, doc_name, doc_path, signed_at, verification_count, category, is_favorite in recent_docs:
                self.create_activity_item(activity_content, doc_name, signed_at, verification_count)
        else:
            no_activity = tk.Label(activity_content,
                                  text="No recent activity. Sign your first document to get started!",
                                  font=(Fonts.PRIMARY, Fonts.BODY),
                                  fg=Colors.TEXT_SECONDARY, bg=Colors.BG_CARD)
            no_activity.pack(pady=20)

    def create_activity_item(self, parent, doc_name, signed_at, verification_count):
        """Create activity item"""
        item_frame = tk.Frame(parent, bg=Colors.BG_MAIN)
        item_frame.pack(fill='x', pady=5)

        # Icon
        tk.Label(item_frame, text="📄",
                font=(Fonts.PRIMARY, 14),
                bg=Colors.BG_MAIN).pack(side='left', padx=(0, 10))

        # Content
        content_frame = tk.Frame(item_frame, bg=Colors.BG_MAIN)
        content_frame.pack(side='left', fill='x', expand=True)

        # Document name
        tk.Label(content_frame, text=doc_name,
                font=(Fonts.PRIMARY, Fonts.BODY, 'bold'),
                fg=Colors.TEXT_PRIMARY, bg=Colors.BG_MAIN).pack(anchor='w')

        # Details
        details = f"Signed on {signed_at[:10]} • {verification_count} verifications"
        tk.Label(content_frame, text=details,
                font=(Fonts.PRIMARY, Fonts.SMALL),
                fg=Colors.TEXT_SECONDARY, bg=Colors.BG_MAIN).pack(anchor='w')

    def create_dashboard_quick_actions(self, parent):
        """Create quick actions section"""
        actions_frame = tk.Frame(parent, bg=Colors.BG_CARD, relief='flat', bd=1)
        actions_frame.pack(fill='x')

        # Header
        header_frame = tk.Frame(actions_frame, bg=Colors.BG_CARD)
        header_frame.pack(fill='x', padx=30, pady=(20, 10))

        tk.Label(header_frame, text="⚡ Quick Actions",
                font=(Fonts.PRIMARY, Fonts.HEADING, 'bold'),
                fg=Colors.TEXT_PRIMARY, bg=Colors.BG_CARD).pack(side='left')

        # Actions buttons
        buttons_frame = tk.Frame(actions_frame, bg=Colors.BG_CARD)
        buttons_frame.pack(fill='x', padx=30, pady=(0, 30))

        # Quick action buttons
        actions = [
            ("✍️ Sign New Document", lambda: self.handle_navigation('sign'), Colors.BUTTON_SUCCESS),
            ("✅ Verify Document", lambda: self.handle_navigation('verify'), Colors.INFO),
            ("📄 View Documents", lambda: self.handle_navigation('documents'), Colors.BUTTON_PRIMARY),
            ("📈 View Analytics", lambda: self.handle_navigation('analytics'), Colors.WARNING)
        ]

        for i, (text, command, color) in enumerate(actions):
            btn = self.create_modern_button(buttons_frame, text, command, color)
            btn.grid(row=i//2, column=i%2, padx=10, pady=10, sticky='ew')

        # Configure grid
        buttons_frame.grid_columnconfigure(0, weight=1)
        buttons_frame.grid_columnconfigure(1, weight=1)

    def show_documents_view(self):
        """Show modern documents management view"""
        # Use existing documents functionality but with modern layout
        self.create_documents_tab()

    def show_sign_view(self):
        """Show modern document signing view"""
        # Use existing signing functionality but with modern layout
        self.create_signing_section(self.scrollable_content)

    def show_verify_view(self):
        """Show modern document verification view"""
        # Use existing verification functionality but with modern layout
        self.create_verification_section(self.scrollable_content)

    def show_analytics_view(self):
        """Show analytics view"""
        analytics_container = tk.Frame(self.scrollable_content, bg=Colors.BG_MAIN)
        analytics_container.pack(fill='both', expand=True, padx=20, pady=20)

        # Coming soon message
        coming_soon = tk.Frame(analytics_container, bg=Colors.BG_CARD, relief='flat', bd=1)
        coming_soon.pack(fill='both', expand=True)

        tk.Label(coming_soon, text="📈 Advanced Analytics",
                font=(Fonts.PRIMARY, Fonts.TITLE, 'bold'),
                fg=Colors.TEXT_PRIMARY, bg=Colors.BG_CARD).pack(pady=(50, 20))

        tk.Label(coming_soon, text="Detailed analytics and reporting features coming soon!",
                font=(Fonts.PRIMARY, Fonts.BODY),
                fg=Colors.TEXT_SECONDARY, bg=Colors.BG_CARD).pack(pady=(0, 50))

    def show_settings_view(self):
        """Show settings view"""
        settings_container = tk.Frame(self.scrollable_content, bg=Colors.BG_MAIN)
        settings_container.pack(fill='both', expand=True, padx=20, pady=20)

        # Settings sections
        self.create_user_settings(settings_container)
        self.create_system_settings(settings_container)

    def create_user_settings(self, parent):
        """Create user settings section"""
        user_frame = tk.Frame(parent, bg=Colors.BG_CARD, relief='flat', bd=1)
        user_frame.pack(fill='x', pady=(0, 20))

        # Header
        header_frame = tk.Frame(user_frame, bg=Colors.BG_CARD)
        header_frame.pack(fill='x', padx=30, pady=(20, 10))

        tk.Label(header_frame, text="👤 User Settings",
                font=(Fonts.PRIMARY, Fonts.HEADING, 'bold'),
                fg=Colors.TEXT_PRIMARY, bg=Colors.BG_CARD).pack(side='left')

        # Settings content
        content_frame = tk.Frame(user_frame, bg=Colors.BG_CARD)
        content_frame.pack(fill='x', padx=30, pady=(0, 30))

        # User info
        info_text = f"Username: {self.current_user}\nStatus: Active\nLast Login: Today"
        tk.Label(content_frame, text=info_text,
                font=(Fonts.PRIMARY, Fonts.BODY),
                fg=Colors.TEXT_PRIMARY, bg=Colors.BG_CARD,
                justify='left').pack(anchor='w', pady=10)

        # Logout button
        logout_btn = self.create_modern_button(content_frame, "🚪 Logout",
                                              self.perform_logout, Colors.ERROR)
        logout_btn.pack(anchor='w', pady=10)

    def create_system_settings(self, parent):
        """Create system settings section"""
        system_frame = tk.Frame(parent, bg=Colors.BG_CARD, relief='flat', bd=1)
        system_frame.pack(fill='x')

        # Header
        header_frame = tk.Frame(system_frame, bg=Colors.BG_CARD)
        header_frame.pack(fill='x', padx=30, pady=(20, 10))

        tk.Label(header_frame, text="⚙️ System Settings",
                font=(Fonts.PRIMARY, Fonts.HEADING, 'bold'),
                fg=Colors.TEXT_PRIMARY, bg=Colors.BG_CARD).pack(side='left')

        # Settings content
        content_frame = tk.Frame(system_frame, bg=Colors.BG_CARD)
        content_frame.pack(fill='x', padx=30, pady=(0, 30))

        # System info
        system_info = "PKI System v2.0\nDatabase: Connected\nSecurity: RSA-2048 + SHA-256"
        tk.Label(content_frame, text=system_info,
                font=(Fonts.PRIMARY, Fonts.BODY),
                fg=Colors.TEXT_PRIMARY, bg=Colors.BG_CARD,
                justify='left').pack(anchor='w', pady=10)

        # System actions
        actions_frame = tk.Frame(content_frame, bg=Colors.BG_CARD)
        actions_frame.pack(anchor='w', pady=10)

        refresh_btn = self.create_modern_button(actions_frame, "🔄 Refresh System",
                                               self.refresh_interface, Colors.BUTTON_PRIMARY)
        refresh_btn.pack(side='left', padx=(0, 10))

        db_btn = self.create_modern_button(actions_frame, "🗄️ View Database",
                                          self.open_database_viewer, Colors.INFO)
        db_btn.pack(side='left')

    def refresh_interface(self):
        """Refresh the interface"""
        self.handle_navigation(self.current_view)
        messagebox.showinfo("Refresh", "Interface refreshed successfully!")

    def open_database_viewer(self):
        """Open database viewer"""
        try:
            import subprocess
            subprocess.Popen(['python', 'database_viewer.py'])
            messagebox.showinfo("Database Viewer", "Database viewer opened in a separate window.")
        except Exception as e:
            messagebox.showerror("Error", f"Could not open database viewer: {str(e)}")

    def export_analytics(self):
        """Export analytics data"""
        messagebox.showinfo("Export", "Analytics export feature coming soon!")

    def create_user_tabs(self):
        """Create tabbed interface for user dashboard"""
        # Create notebook (tabbed interface)
        self.user_notebook = ttk.Notebook(self.content_frame)
        self.user_notebook.pack(fill='both', expand=True, padx=20, pady=10)

        # Dashboard Tab
        self.dashboard_frame = tk.Frame(self.user_notebook, bg=Colors.BG_MAIN)
        self.user_notebook.add(self.dashboard_frame, text="📊 Dashboard")

        # Documents Tab
        self.documents_frame = tk.Frame(self.user_notebook, bg=Colors.BG_MAIN)
        self.user_notebook.add(self.documents_frame, text="📄 Documents")

        # Settings Tab
        self.settings_frame = tk.Frame(self.user_notebook, bg=Colors.BG_MAIN)
        self.user_notebook.add(self.settings_frame, text="⚙️ Settings")

        # Create content for each tab
        self.create_dashboard_tab()
        self.create_documents_tab()
        self.create_settings_tab()

    def create_dashboard_tab(self):
        """Create the main dashboard with analytics"""
        # Create scrollable frame for dashboard
        dashboard_canvas = tk.Canvas(self.dashboard_frame, bg=Colors.BG_MAIN, highlightthickness=0)
        dashboard_scrollbar = ttk.Scrollbar(self.dashboard_frame, orient="vertical", command=dashboard_canvas.yview)
        dashboard_scrollable = tk.Frame(dashboard_canvas, bg=Colors.BG_MAIN)

        dashboard_scrollable.bind(
            "<Configure>",
            lambda e: dashboard_canvas.configure(scrollregion=dashboard_canvas.bbox("all"))
        )

        dashboard_canvas.create_window((0, 0), window=dashboard_scrollable, anchor="nw")
        dashboard_canvas.configure(yscrollcommand=dashboard_scrollbar.set)

        dashboard_canvas.pack(side="left", fill="both", expand=True)
        dashboard_scrollbar.pack(side="right", fill="y")

        # Welcome header
        self.create_dashboard_header(dashboard_scrollable)

        # Statistics cards
        self.create_statistics_cards(dashboard_scrollable)

        # Activity charts
        self.create_activity_charts(dashboard_scrollable)

        # Recent activity
        self.create_recent_activity(dashboard_scrollable)

        # Certificate status
        self.create_certificate_status(dashboard_scrollable)

    def create_dashboard_header(self, parent):
        """Create dashboard welcome header"""
        header_frame = tk.Frame(parent, bg=Colors.PRIMARY_DARK, height=80)
        header_frame.pack(fill='x', pady=(0, 20), padx=20)
        header_frame.pack_propagate(False)

        # Welcome message
        welcome_label = tk.Label(header_frame,
                                text=f"Welcome back, {self.current_user}!",
                                font=(Fonts.PRIMARY, Fonts.TITLE, 'bold'),
                                fg=Colors.TEXT_WHITE, bg=Colors.PRIMARY_DARK)
        welcome_label.pack(expand=True, pady=20)

    def create_statistics_cards(self, parent):
        """Create statistics cards showing user metrics"""
        # Get user dashboard data
        dashboard_data = self.db.get_user_dashboard_data(self.current_user_id)

        # Statistics container
        stats_container = tk.Frame(parent, bg=Colors.BG_MAIN)
        stats_container.pack(fill='x', padx=20, pady=(0, 20))

        # Create individual stat cards
        stats = [
            ("Documents Signed", dashboard_data.get('documents_signed', 0), Colors.SUCCESS, "📄"),
            ("Total Verifications", dashboard_data.get('verification_stats', {}).get('total', 0), Colors.INFO, "✅"),
            ("Average Verifications", dashboard_data.get('verification_stats', {}).get('average', 0), Colors.WARNING, "📊"),
            ("Documents Verified", dashboard_data.get('documents_verified', 0), Colors.LIGHT_BLUE, "🔍")
        ]

        for i, (title, value, color, icon) in enumerate(stats):
            self.create_stat_card(stats_container, title, value, color, icon, i)

    def create_stat_card(self, parent, title, value, color, icon, position):
        """Create individual statistics card"""
        # Card frame
        card_frame = tk.Frame(parent, bg=Colors.BG_CARD, relief='flat', bd=1)
        card_frame.grid(row=0, column=position, padx=10, pady=10, sticky='ew')

        # Configure grid weights
        parent.grid_columnconfigure(position, weight=1)

        # Header with color accent
        header_frame = tk.Frame(card_frame, bg=color, height=40)
        header_frame.pack(fill='x')
        header_frame.pack_propagate(False)

        # Icon and title
        header_content = tk.Frame(header_frame, bg=color)
        header_content.pack(expand=True, fill='both')

        icon_label = tk.Label(header_content, text=icon,
                             font=(Fonts.PRIMARY, 16),
                             fg=Colors.TEXT_WHITE, bg=color)
        icon_label.pack(side='left', padx=10, pady=8)

        title_label = tk.Label(header_content, text=title,
                              font=(Fonts.PRIMARY, Fonts.SMALL, 'bold'),
                              fg=Colors.TEXT_WHITE, bg=color)
        title_label.pack(side='left', pady=8)

        # Value display
        value_frame = tk.Frame(card_frame, bg=Colors.BG_CARD)
        value_frame.pack(fill='x', pady=15)

        value_label = tk.Label(value_frame, text=str(value),
                              font=(Fonts.PRIMARY, Fonts.TITLE, 'bold'),
                              fg=color, bg=Colors.BG_CARD)
        value_label.pack()

    def create_activity_charts(self, parent):
        """Create activity charts section"""
        # Get dashboard data
        dashboard_data = self.db.get_user_dashboard_data(self.current_user_id)
        monthly_activity = dashboard_data.get('monthly_activity', [])

        # Charts container
        charts_frame = self.create_section_frame(parent, "📈 Activity Overview", Colors.INFO)

        if monthly_activity:
            # Create simple text-based chart
            chart_text = "Monthly Signing Activity (Last 12 Months):\n\n"
            max_count = max([count for _, count in monthly_activity]) if monthly_activity else 1

            for month, count in monthly_activity:
                bar_length = int((count / max_count) * 30) if max_count > 0 else 0
                bar = "█" * bar_length + "░" * (30 - bar_length)
                chart_text += f"{month}: {bar} ({count})\n"

            chart_label = tk.Label(charts_frame, text=chart_text,
                                  font=(Fonts.SECONDARY, Fonts.SMALL),
                                  fg=Colors.TEXT_PRIMARY, bg=Colors.BG_CARD,
                                  justify='left', anchor='w')
            chart_label.pack(anchor='w', padx=10, pady=10)
        else:
            no_data_label = tk.Label(charts_frame, text="No activity data available yet.\nStart signing documents to see your activity chart!",
                                    font=(Fonts.PRIMARY, Fonts.BODY),
                                    fg=Colors.TEXT_SECONDARY, bg=Colors.BG_CARD,
                                    justify='center')
            no_data_label.pack(pady=20)

    def create_recent_activity(self, parent):
        """Create recent activity section"""
        # Get dashboard data
        dashboard_data = self.db.get_user_dashboard_data(self.current_user_id)
        recent_docs = dashboard_data.get('recent_documents', [])

        # Recent activity container
        activity_frame = self.create_section_frame(parent, "📋 Recent Documents", Colors.SUCCESS)

        if recent_docs:
            # Create table-like display
            for doc_name, signed_at, verification_count in recent_docs[:5]:  # Show top 5
                doc_row = tk.Frame(activity_frame, bg=Colors.BG_CARD)
                doc_row.pack(fill='x', pady=2, padx=10)

                # Document icon and name
                doc_info = tk.Frame(doc_row, bg=Colors.BG_CARD)
                doc_info.pack(side='left', fill='x', expand=True)

                tk.Label(doc_info, text="📄", font=(Fonts.PRIMARY, 12),
                        bg=Colors.BG_CARD).pack(side='left', padx=(0, 5))

                tk.Label(doc_info, text=doc_name,
                        font=(Fonts.PRIMARY, Fonts.BODY),
                        fg=Colors.TEXT_PRIMARY, bg=Colors.BG_CARD).pack(side='left')

                # Stats
                stats_info = tk.Frame(doc_row, bg=Colors.BG_CARD)
                stats_info.pack(side='right')

                tk.Label(stats_info, text=f"✅ {verification_count}",
                        font=(Fonts.PRIMARY, Fonts.SMALL),
                        fg=Colors.SUCCESS, bg=Colors.BG_CARD).pack(side='right', padx=5)

                tk.Label(stats_info, text=signed_at[:10],
                        font=(Fonts.PRIMARY, Fonts.SMALL),
                        fg=Colors.TEXT_SECONDARY, bg=Colors.BG_CARD).pack(side='right', padx=5)
        else:
            no_docs_label = tk.Label(activity_frame, text="No documents signed yet.\nSign your first document to see it here!",
                                    font=(Fonts.PRIMARY, Fonts.BODY),
                                    fg=Colors.TEXT_SECONDARY, bg=Colors.BG_CARD,
                                    justify='center')
            no_docs_label.pack(pady=20)

    def create_certificate_status(self, parent):
        """Create certificate status monitoring"""
        # Get dashboard data
        dashboard_data = self.db.get_user_dashboard_data(self.current_user_id)
        cert_info = dashboard_data.get('certificate', {})

        # Certificate status container
        cert_frame = self.create_section_frame(parent, "🔐 Certificate Status", Colors.WARNING)

        if cert_info:
            # Certificate details
            details_frame = tk.Frame(cert_frame, bg=Colors.BG_CARD)
            details_frame.pack(fill='x', padx=10, pady=10)

            # Serial number
            serial_row = tk.Frame(details_frame, bg=Colors.BG_CARD)
            serial_row.pack(fill='x', pady=2)
            tk.Label(serial_row, text="Serial Number:",
                    font=(Fonts.PRIMARY, Fonts.BODY, 'bold'),
                    fg=Colors.TEXT_PRIMARY, bg=Colors.BG_CARD).pack(side='left')
            tk.Label(serial_row, text=cert_info.get('serial_number', 'N/A'),
                    font=(Fonts.SECONDARY, Fonts.SMALL),
                    fg=Colors.TEXT_SECONDARY, bg=Colors.BG_CARD).pack(side='right')

            # Subject name
            subject_row = tk.Frame(details_frame, bg=Colors.BG_CARD)
            subject_row.pack(fill='x', pady=2)
            tk.Label(subject_row, text="Subject:",
                    font=(Fonts.PRIMARY, Fonts.BODY, 'bold'),
                    fg=Colors.TEXT_PRIMARY, bg=Colors.BG_CARD).pack(side='left')
            tk.Label(subject_row, text=cert_info.get('subject_name', 'N/A'),
                    font=(Fonts.PRIMARY, Fonts.SMALL),
                    fg=Colors.TEXT_SECONDARY, bg=Colors.BG_CARD).pack(side='right')

            # Validity period
            valid_row = tk.Frame(details_frame, bg=Colors.BG_CARD)
            valid_row.pack(fill='x', pady=2)
            tk.Label(valid_row, text="Valid Until:",
                    font=(Fonts.PRIMARY, Fonts.BODY, 'bold'),
                    fg=Colors.TEXT_PRIMARY, bg=Colors.BG_CARD).pack(side='left')

            valid_until = cert_info.get('valid_until', '')
            if valid_until:
                # Check if certificate is expiring soon (within 30 days)
                try:
                    from datetime import datetime
                    if isinstance(valid_until, str):
                        valid_date = datetime.fromisoformat(valid_until.replace('Z', '+00:00'))
                    else:
                        valid_date = valid_until

                    days_until_expiry = (valid_date - datetime.now(timezone.utc)).days

                    if days_until_expiry < 30:
                        color = Colors.ERROR
                        status = f"⚠️ Expires in {days_until_expiry} days"
                    elif days_until_expiry < 90:
                        color = Colors.WARNING
                        status = f"⚠️ Expires in {days_until_expiry} days"
                    else:
                        color = Colors.SUCCESS
                        status = f"✅ Valid ({days_until_expiry} days remaining)"
                except:
                    color = Colors.TEXT_SECONDARY
                    status = str(valid_until)[:10]
            else:
                color = Colors.TEXT_SECONDARY
                status = "N/A"

            tk.Label(valid_row, text=status,
                    font=(Fonts.PRIMARY, Fonts.SMALL),
                    fg=color, bg=Colors.BG_CARD).pack(side='right')
        else:
            no_cert_label = tk.Label(cert_frame, text="No certificate information available.",
                                    font=(Fonts.PRIMARY, Fonts.BODY),
                                    fg=Colors.TEXT_SECONDARY, bg=Colors.BG_CARD)
            no_cert_label.pack(pady=20)

    def create_documents_tab(self):
        """Create enhanced documents management tab"""
        # Use the scrollable content area
        docs_scrollable = self.scrollable_content

        # Smart document management sections
        self.create_quick_actions(docs_scrollable)
        self.create_recent_documents_section(docs_scrollable)
        self.create_favorites_section(docs_scrollable)
        self.create_categories_section(docs_scrollable)

    def create_quick_actions(self, parent):
        """Create quick actions section"""
        actions_frame = self.create_section_frame(parent, "⚡ Quick Actions", Colors.INFO)

        # Search bar
        search_container = tk.Frame(actions_frame, bg=Colors.BG_CARD)
        search_container.pack(fill='x', pady=(0, 15))

        tk.Label(search_container, text="🔍 Search Documents:",
                font=(Fonts.PRIMARY, Fonts.BODY, 'bold'),
                fg=Colors.TEXT_PRIMARY, bg=Colors.BG_CARD).pack(anchor='w')

        search_frame = tk.Frame(search_container, bg=Colors.BG_CARD)
        search_frame.pack(fill='x', pady=(5, 0))

        self.search_entry = tk.Entry(search_frame,
                                    font=(Fonts.PRIMARY, Fonts.BODY),
                                    bg=Colors.BG_MAIN, fg=Colors.TEXT_PRIMARY,
                                    relief='flat', bd=1)
        self.search_entry.pack(side='left', fill='x', expand=True, padx=(0, 10), ipady=5)
        self.search_entry.bind('<KeyRelease>', self.on_search_change)

        search_btn = self.create_modern_button(search_frame, "Search",
                                              self.perform_document_search, Colors.INFO)
        search_btn.pack(side='right')

        # Quick action buttons
        buttons_frame = tk.Frame(actions_frame, bg=Colors.BG_CARD)
        buttons_frame.pack(fill='x', pady=(10, 0))

        refresh_btn = self.create_modern_button(buttons_frame, "🔄 Refresh",
                                               self.refresh_documents, Colors.BUTTON_PRIMARY)
        refresh_btn.pack(side='left', padx=(0, 10))

        favorites_btn = self.create_modern_button(buttons_frame, "⭐ Show Favorites",
                                                 self.show_favorites, Colors.WARNING)
        favorites_btn.pack(side='left', padx=(0, 10))

        categories_btn = self.create_modern_button(buttons_frame, "📁 Categories",
                                                  self.show_categories, Colors.SUCCESS)
        categories_btn.pack(side='left')

    def create_recent_documents_section(self, parent):
        """Create recent documents section"""
        recent_frame = self.create_section_frame(parent, "📄 Recent Documents", Colors.SUCCESS)

        # Get recent documents
        recent_docs = self.db.get_recent_documents(self.current_user_id, 5)

        if recent_docs:
            for doc_id, doc_name, doc_path, signed_at, verification_count, category, is_favorite in recent_docs:
                self.create_document_item(recent_frame, doc_id, doc_name, doc_path,
                                        signed_at, verification_count, category, is_favorite)
        else:
            no_docs_label = tk.Label(recent_frame, text="No recent documents found.\nSign your first document to see it here!",
                                    font=(Fonts.PRIMARY, Fonts.BODY),
                                    fg=Colors.TEXT_SECONDARY, bg=Colors.BG_CARD,
                                    justify='center')
            no_docs_label.pack(pady=20)

    def create_favorites_section(self, parent):
        """Create favorites section"""
        favorites_frame = self.create_section_frame(parent, "⭐ Favorite Documents", Colors.WARNING)

        # Get favorite documents
        favorite_docs = self.db.get_favorite_documents(self.current_user_id)

        if favorite_docs:
            for doc_id, doc_name, doc_path, signed_at, verification_count, category in favorite_docs:
                self.create_document_item(favorites_frame, doc_id, doc_name, doc_path,
                                        signed_at, verification_count, category, True)
        else:
            no_favorites_label = tk.Label(favorites_frame, text="No favorite documents yet.\nClick the ⭐ button on any document to add it to favorites!",
                                         font=(Fonts.PRIMARY, Fonts.BODY),
                                         fg=Colors.TEXT_SECONDARY, bg=Colors.BG_CARD,
                                         justify='center')
            no_favorites_label.pack(pady=20)

    def create_categories_section(self, parent):
        """Create categories section"""
        categories_frame = self.create_section_frame(parent, "📁 Document Categories", Colors.LIGHT_BLUE)

        # Get categories
        categories = self.db.get_document_categories(self.current_user_id)

        if categories:
            for category, count in categories:
                cat_row = tk.Frame(categories_frame, bg=Colors.BG_CARD)
                cat_row.pack(fill='x', pady=2, padx=10)

                # Category info
                cat_info = tk.Frame(cat_row, bg=Colors.BG_CARD)
                cat_info.pack(side='left', fill='x', expand=True)

                tk.Label(cat_info, text="📁", font=(Fonts.PRIMARY, 12),
                        bg=Colors.BG_CARD).pack(side='left', padx=(0, 5))

                tk.Label(cat_info, text=f"{category} ({count})",
                        font=(Fonts.PRIMARY, Fonts.BODY),
                        fg=Colors.TEXT_PRIMARY, bg=Colors.BG_CARD).pack(side='left')

                # View button
                view_btn = self.create_modern_button(cat_row, "View",
                                                    lambda c=category: self.show_category_documents(c),
                                                    Colors.LIGHT_BLUE)
                view_btn.pack(side='right')
        else:
            no_categories_label = tk.Label(categories_frame, text="No categories yet.\nDocuments will be automatically categorized as you sign them!",
                                          font=(Fonts.PRIMARY, Fonts.BODY),
                                          fg=Colors.TEXT_SECONDARY, bg=Colors.BG_CARD,
                                          justify='center')
            no_categories_label.pack(pady=20)

    def create_document_item(self, parent, doc_id, doc_name, doc_path, signed_at, verification_count, category, is_favorite):
        """Create a document item display"""
        item_frame = tk.Frame(parent, bg=Colors.BG_MAIN, relief='flat', bd=1)
        item_frame.pack(fill='x', pady=2, padx=10)

        # Document info
        info_frame = tk.Frame(item_frame, bg=Colors.BG_MAIN)
        info_frame.pack(side='left', fill='x', expand=True, padx=10, pady=5)

        # Name and category
        name_frame = tk.Frame(info_frame, bg=Colors.BG_MAIN)
        name_frame.pack(fill='x')

        tk.Label(name_frame, text="📄", font=(Fonts.PRIMARY, 12),
                bg=Colors.BG_MAIN).pack(side='left', padx=(0, 5))

        tk.Label(name_frame, text=doc_name,
                font=(Fonts.PRIMARY, Fonts.BODY, 'bold'),
                fg=Colors.TEXT_PRIMARY, bg=Colors.BG_MAIN).pack(side='left')

        tk.Label(name_frame, text=f"[{category}]",
                font=(Fonts.PRIMARY, Fonts.SMALL),
                fg=Colors.TEXT_SECONDARY, bg=Colors.BG_MAIN).pack(side='right')

        # Stats
        stats_frame = tk.Frame(info_frame, bg=Colors.BG_MAIN)
        stats_frame.pack(fill='x', pady=(2, 0))

        tk.Label(stats_frame, text=f"Signed: {signed_at[:10]}",
                font=(Fonts.PRIMARY, Fonts.SMALL),
                fg=Colors.TEXT_SECONDARY, bg=Colors.BG_MAIN).pack(side='left')

        tk.Label(stats_frame, text=f"✅ {verification_count} verifications",
                font=(Fonts.PRIMARY, Fonts.SMALL),
                fg=Colors.SUCCESS, bg=Colors.BG_MAIN).pack(side='right')

        # Action buttons
        actions_frame = tk.Frame(item_frame, bg=Colors.BG_MAIN)
        actions_frame.pack(side='right', padx=10, pady=5)

        # Favorite button
        fav_color = Colors.WARNING if is_favorite else Colors.TEXT_SECONDARY
        fav_btn = tk.Button(actions_frame, text="⭐",
                           command=lambda: self.toggle_favorite(doc_id),
                           bg=Colors.BG_MAIN, fg=fav_color,
                           font=(Fonts.PRIMARY, 12), relief='flat', bd=0,
                           cursor='hand2')
        fav_btn.pack(side='left', padx=2)

        # Re-sign button
        resign_btn = self.create_modern_button(actions_frame, "Re-sign",
                                              lambda: self.resign_document(doc_path),
                                              Colors.SUCCESS)
        resign_btn.pack(side='left', padx=2)

    def on_search_change(self, event):
        """Handle search input changes"""
        # Implement real-time search if needed
        pass

    def perform_document_search(self):
        """Perform document search"""
        search_term = self.search_entry.get().strip()
        if search_term:
            results = self.db.search_documents(self.current_user_id, search_term)
            self.show_search_results(results, search_term)
        else:
            messagebox.showinfo("Search", "Please enter a search term.")

    def show_search_results(self, results, search_term):
        """Show search results in a new window"""
        if not results:
            messagebox.showinfo("Search Results", f"No documents found for '{search_term}'.")
            return

        # Create search results window
        results_window = tk.Toplevel(self.root)
        results_window.title(f"Search Results: '{search_term}'")
        results_window.geometry("800x600")
        results_window.configure(bg=Colors.BG_MAIN)

        # Results header
        header_label = tk.Label(results_window,
                               text=f"Found {len(results)} documents for '{search_term}'",
                               font=(Fonts.PRIMARY, Fonts.HEADING, 'bold'),
                               fg=Colors.TEXT_PRIMARY, bg=Colors.BG_MAIN)
        header_label.pack(pady=20)

        # Results list
        results_frame = tk.Frame(results_window, bg=Colors.BG_MAIN)
        results_frame.pack(fill='both', expand=True, padx=20, pady=(0, 20))

        for doc_id, doc_name, doc_path, signed_at, verification_count, category, is_favorite in results:
            self.create_document_item(results_frame, doc_id, doc_name, doc_path,
                                    signed_at, verification_count, category, is_favorite)

    def refresh_documents(self):
        """Refresh document lists"""
        # Recreate the documents tab
        for widget in self.documents_frame.winfo_children():
            widget.destroy()
        self.create_documents_tab()
        messagebox.showinfo("Refresh", "Document lists refreshed successfully!")

    def show_favorites(self):
        """Show only favorite documents"""
        favorites = self.db.get_favorite_documents(self.current_user_id)
        if favorites:
            self.show_document_list(favorites, "⭐ Favorite Documents")
        else:
            messagebox.showinfo("Favorites", "No favorite documents found.")

    def show_categories(self):
        """Show category management dialog"""
        categories = self.db.get_document_categories(self.current_user_id)
        if categories:
            category_text = "Document Categories:\n\n"
            for category, count in categories:
                category_text += f"📁 {category}: {count} documents\n"
            messagebox.showinfo("Categories", category_text)
        else:
            messagebox.showinfo("Categories", "No categories found.")

    def show_category_documents(self, category):
        """Show documents in a specific category"""
        docs = self.db.get_documents_by_category(self.current_user_id, category)
        if docs:
            self.show_document_list(docs, f"📁 Category: {category}")
        else:
            messagebox.showinfo("Category", f"No documents found in category '{category}'.")

    def show_document_list(self, documents, title):
        """Show a list of documents in a new window"""
        # Create document list window
        list_window = tk.Toplevel(self.root)
        list_window.title(title)
        list_window.geometry("900x700")
        list_window.configure(bg=Colors.BG_MAIN)

        # Header
        header_label = tk.Label(list_window, text=title,
                               font=(Fonts.PRIMARY, Fonts.HEADING, 'bold'),
                               fg=Colors.TEXT_PRIMARY, bg=Colors.BG_MAIN)
        header_label.pack(pady=20)

        # Document list
        list_frame = tk.Frame(list_window, bg=Colors.BG_MAIN)
        list_frame.pack(fill='both', expand=True, padx=20, pady=(0, 20))

        for doc_data in documents:
            if len(doc_data) == 6:  # Favorites format
                doc_id, doc_name, doc_path, signed_at, verification_count, category = doc_data
                is_favorite = True
            else:  # Regular format
                doc_id, doc_name, doc_path, signed_at, verification_count, category, is_favorite = doc_data

            self.create_document_item(list_frame, doc_id, doc_name, doc_path,
                                    signed_at, verification_count, category, is_favorite)

    def toggle_favorite(self, document_id):
        """Toggle favorite status of a document"""
        self.db.toggle_document_favorite(document_id)
        self.refresh_documents()
        messagebox.showinfo("Favorite", "Document favorite status updated!")

    def resign_document(self, document_path):
        """Re-sign an existing document"""
        if os.path.exists(document_path):
            self.document_path = document_path
            filename = os.path.basename(document_path)
            self.doc_path_label.config(text=f"📄 {filename}",
                                      fg=Colors.SUCCESS, bg=Colors.BG_MAIN)
            self.animate_selection_feedback(self.doc_path_label)

            # Switch to signing tab
            self.user_notebook.select(1)  # Documents tab
            messagebox.showinfo("Re-sign", f"Document '{filename}' loaded for re-signing.\nClick 'Sign Document' to create a new signature.")
        else:
            messagebox.showerror("Error", f"Document file not found: {document_path}")

    def create_settings_tab(self):
        """Create settings and logout tab"""
        self.create_logout_section(self.settings_frame)

    def create_logout_section(self, parent):
        """Create modern logout section"""
        logout_frame = self.create_section_frame(parent, "🚪 Session Management", Colors.ERROR)

        # Description with modern typography
        desc_label = tk.Label(logout_frame,
                             text="End your current session and return to the main authentication interface",
                             font=(Fonts.PRIMARY, Fonts.BODY),
                             fg=Colors.TEXT_SECONDARY, bg=Colors.BG_CARD,
                             wraplength=600, justify='center')
        desc_label.pack(pady=(0, 25))

        # Modern logout button
        button_container = tk.Frame(logout_frame, bg=Colors.BG_CARD)
        button_container.pack(pady=15)

        logout_button = self.create_modern_button(button_container, "🔓 Logout from System",
                                                 self.perform_logout, Colors.BUTTON_DANGER)
        logout_button.pack()
    
    def create_section_frame(self, parent, title, color):
        """Create a modern responsive card-based section frame"""
        # Get responsive padding
        window_width = self.root.winfo_width() if self.root.winfo_width() > 1 else 1400

        if window_width < 1000:
            container_padx = 10
            content_padx = 15
            content_pady = 15
        elif window_width < 1300:
            container_padx = 15
            content_padx = 20
            content_pady = 18
        else:
            container_padx = 20
            content_padx = 25
            content_pady = 20

        # Container for shadow effect - responsive padding
        container = tk.Frame(parent, bg=Colors.BG_MAIN)
        container.pack(fill='both', expand=True, pady=15, padx=container_padx)

        # Shadow frame
        shadow_frame = tk.Frame(container, bg=Colors.BORDER_LIGHT, height=2)
        shadow_frame.pack(fill='x', pady=(2, 0))

        # Main card frame - ensure it fills width
        section_frame = tk.Frame(container, bg=Colors.BG_CARD, relief='flat', bd=0)
        section_frame.pack(fill='both', expand=True)

        # Header section with colored accent - responsive height
        header_height = 45 if window_width < 1000 else 50
        header_frame = tk.Frame(section_frame, bg=color, height=header_height)
        header_frame.pack(fill='x')
        header_frame.pack_propagate(False)

        # Title with responsive typography
        font_size = Fonts.SUBHEADING if window_width < 1000 else Fonts.HEADING
        title_label = tk.Label(header_frame, text=title,
                              font=(Fonts.PRIMARY, font_size, 'bold'),
                              fg=Colors.TEXT_WHITE, bg=color)
        title_label.pack(expand=True, pady=10)

        # Content area - responsive padding
        content_frame = tk.Frame(section_frame, bg=Colors.BG_CARD)
        content_frame.pack(fill='both', expand=True, padx=content_padx, pady=content_pady)

        return content_frame

    def create_modern_button(self, parent, text, command, bg_color, hover_color=None):
        """Create a modern responsive button with hover effects"""
        if hover_color is None:
            hover_color = Colors.BUTTON_HOVER

        # Responsive button sizing
        window_width = self.root.winfo_width() if self.root.winfo_width() > 1 else 1400

        if window_width < 1000:
            padx, pady = 20, 10
            font_size = Fonts.SMALL
        elif window_width < 1300:
            padx, pady = 22, 11
            font_size = Fonts.BODY
        else:
            padx, pady = 25, 12
            font_size = Fonts.BODY

        button = tk.Button(parent, text=text, command=command,
                          bg=bg_color, fg=Colors.TEXT_WHITE,
                          font=(Fonts.PRIMARY, font_size, 'bold'),
                          relief='flat', bd=0, padx=padx, pady=pady,
                          cursor='hand2', activebackground=hover_color,
                          activeforeground=Colors.TEXT_WHITE)

        # Add hover effects
        def on_enter(e):
            button.config(bg=hover_color)

        def on_leave(e):
            button.config(bg=bg_color)

        button.bind("<Enter>", on_enter)
        button.bind("<Leave>", on_leave)

        return button

    def animate_button_click(self, button, original_color, click_color):
        """Animate button click with color change"""
        button.config(bg=click_color)
        self.root.after(100, lambda: button.config(bg=original_color))

    def create_registration_section(self, parent):
        """Create modern user registration section"""
        reg_frame = self.create_section_frame(parent, "👤 User Registration", Colors.INFO)

        # Description with responsive typography
        window_width = self.root.winfo_width() if self.root.winfo_width() > 1 else 1400
        wrap_length = min(600, window_width - 100)

        desc_text = "Create a new user account with RSA-2048 key pair and X.509 certificate"
        if window_width < 1000:
            desc_text = "Create new user with RSA key pair and certificate"

        desc_label = tk.Label(reg_frame, text=desc_text,
                             font=(Fonts.PRIMARY, Fonts.BODY),
                             fg=Colors.TEXT_SECONDARY, bg=Colors.BG_CARD,
                             wraplength=wrap_length, justify='center')
        desc_label.pack(pady=(0, 20))

        # Modern input container
        input_container = tk.Frame(reg_frame, bg=Colors.BG_CARD)
        input_container.pack(pady=10)

        # Username input with modern styling
        input_frame = tk.Frame(input_container, bg=Colors.BG_CARD)
        input_frame.pack()

        username_label = tk.Label(input_frame, text="Username:",
                                 font=(Fonts.PRIMARY, Fonts.BODY, 'bold'),
                                 fg=Colors.TEXT_PRIMARY, bg=Colors.BG_CARD)
        username_label.pack(side='left', padx=(0, 10))

        # Modern entry field
        self.username_entry = tk.Entry(input_frame,
                                      font=(Fonts.PRIMARY, Fonts.BODY),
                                      width=25, relief='flat', bd=1,
                                      bg=Colors.BG_MAIN, fg=Colors.TEXT_PRIMARY,
                                      insertbackground=Colors.TEXT_PRIMARY)
        self.username_entry.pack(side='left', padx=10, ipady=8)

        # Modern register button
        self.register_button = self.create_modern_button(
            input_frame, "Register User", self.register_user, Colors.BUTTON_PRIMARY)
        self.register_button.pack(side='left', padx=15)

        # Status label with modern styling
        self.reg_status_label = tk.Label(reg_frame, text="",
                                        font=(Fonts.PRIMARY, Fonts.BODY),
                                        bg=Colors.BG_CARD, wraplength=600)
        self.reg_status_label.pack(pady=(15, 0))

    def create_login_section(self, parent):
        """Create modern username-based login section"""
        login_frame = self.create_section_frame(parent, "🔑 User Authentication", Colors.WARNING)

        # Description with modern typography
        desc_label = tk.Label(login_frame,
                             text="Enter your username to automatically load your credentials and authenticate",
                             font=(Fonts.PRIMARY, Fonts.BODY),
                             fg=Colors.TEXT_SECONDARY, bg=Colors.BG_CARD,
                             wraplength=600, justify='center')
        desc_label.pack(pady=(0, 25))

        # Modern username input container
        input_container = tk.Frame(login_frame, bg=Colors.BG_CARD)
        input_container.pack(pady=10)

        # Username input with modern styling
        input_frame = tk.Frame(input_container, bg=Colors.BG_CARD)
        input_frame.pack()

        username_label = tk.Label(input_frame, text="Username:",
                                 font=(Fonts.PRIMARY, Fonts.BODY, 'bold'),
                                 fg=Colors.TEXT_PRIMARY, bg=Colors.BG_CARD)
        username_label.pack(side='left', padx=(0, 10))

        # Modern entry field
        self.login_username_entry = tk.Entry(input_frame,
                                           font=(Fonts.PRIMARY, Fonts.BODY),
                                           width=25, relief='flat', bd=1,
                                           bg=Colors.BG_MAIN, fg=Colors.TEXT_PRIMARY,
                                           insertbackground=Colors.TEXT_PRIMARY)
        self.login_username_entry.pack(side='left', padx=10, ipady=8)
        self.login_username_entry.bind('<Return>', lambda e: self.perform_login())

        # Modern login button
        self.login_button = self.create_modern_button(input_frame, "🔓 Login",
                                                     self.perform_login, Colors.BUTTON_SUCCESS)
        self.login_button.pack(side='left', padx=15)

        # Credential status display
        status_container = tk.Frame(login_frame, bg=Colors.BG_CARD)
        status_container.pack(fill='x', pady=(20, 0))

        # Key status
        self.key_status_label = tk.Label(status_container, text="🔑 Private key: Not loaded",
                                        font=(Fonts.PRIMARY, Fonts.SMALL),
                                        fg=Colors.TEXT_SECONDARY, bg=Colors.BG_CARD)
        self.key_status_label.pack(anchor='w', pady=2)

        # Certificate status
        self.cert_status_label = tk.Label(status_container, text="📜 Certificate: Not loaded",
                                         font=(Fonts.PRIMARY, Fonts.SMALL),
                                         fg=Colors.TEXT_SECONDARY, bg=Colors.BG_CARD)
        self.cert_status_label.pack(anchor='w', pady=2)

        # Manual selection option (for advanced users)
        manual_frame = tk.Frame(login_frame, bg=Colors.BG_CARD)
        manual_frame.pack(fill='x', pady=(15, 0))

        manual_label = tk.Label(manual_frame, text="Advanced: Manual file selection",
                               font=(Fonts.PRIMARY, Fonts.SMALL, 'italic'),
                               fg=Colors.TEXT_SECONDARY, bg=Colors.BG_CARD)
        manual_label.pack(anchor='w')

        manual_buttons = tk.Frame(manual_frame, bg=Colors.BG_CARD)
        manual_buttons.pack(anchor='w', pady=(5, 0))

        key_browse_btn = self.create_modern_button(manual_buttons, "Browse Key",
                                                  self.select_private_key, Colors.BUTTON_PRIMARY)
        key_browse_btn.pack(side='left', padx=(0, 10))

        cert_browse_btn = self.create_modern_button(manual_buttons, "Browse Cert",
                                                   self.select_certificate, Colors.WARNING)
        cert_browse_btn.pack(side='left')

        # Status label with modern styling
        self.login_status_label = tk.Label(login_frame, text="",
                                          font=(Fonts.PRIMARY, Fonts.BODY),
                                          bg=Colors.BG_CARD, wraplength=600)
        self.login_status_label.pack(pady=(15, 0))

    def create_signing_section(self, parent):
        """Create modern document signing section (only shown after login)"""
        sign_frame = self.create_section_frame(parent, "✍️ Document Signing", Colors.SUCCESS)

        # Description with modern typography
        desc_label = tk.Label(sign_frame,
                             text="Sign documents with your digital signature using RSA-PSS cryptography",
                             font=(Fonts.PRIMARY, Fonts.BODY),
                             fg=Colors.TEXT_SECONDARY, bg=Colors.BG_CARD,
                             wraplength=600, justify='center')
        desc_label.pack(pady=(0, 25))

        # Modern document selection container
        doc_container = tk.Frame(sign_frame, bg=Colors.BG_CARD)
        doc_container.pack(fill='x', pady=10)

        # Document selection with modern styling
        doc_section = tk.Frame(doc_container, bg=Colors.BG_CARD)
        doc_section.pack(fill='x', pady=8)

        doc_label = tk.Label(doc_section, text="Document to Sign:",
                            font=(Fonts.PRIMARY, Fonts.BODY, 'bold'),
                            fg=Colors.TEXT_PRIMARY, bg=Colors.BG_CARD)
        doc_label.pack(anchor='w')

        doc_row = tk.Frame(doc_section, bg=Colors.BG_CARD)
        doc_row.pack(fill='x', pady=(5, 0))

        self.doc_path_label = tk.Label(doc_row, text="📄 No document selected",
                                      font=(Fonts.PRIMARY, Fonts.SMALL),
                                      fg=Colors.ERROR, bg=Colors.BG_MAIN,
                                      relief='flat', bd=1, padx=10, pady=8,
                                      anchor='w')
        self.doc_path_label.pack(side='left', fill='x', expand=True, padx=(0, 10))

        doc_browse_btn = self.create_modern_button(doc_row, "Browse Documents",
                                                  self.select_document, Colors.SUCCESS)
        doc_browse_btn.pack(side='right')

        # Modern sign button
        button_container = tk.Frame(sign_frame, bg=Colors.BG_CARD)
        button_container.pack(pady=25)

        self.sign_button = self.create_modern_button(button_container, "🖊️ Sign Document",
                                                    self.sign_document, Colors.BUTTON_SUCCESS)
        self.sign_button.pack()

        # Status label with modern styling
        self.sign_status_label = tk.Label(sign_frame, text="Ready to sign documents",
                                         font=(Fonts.PRIMARY, Fonts.BODY),
                                         fg=Colors.SUCCESS, bg=Colors.BG_CARD, wraplength=600)
        self.sign_status_label.pack(pady=(15, 0))

    def create_verification_section(self, parent):
        """Create modern document verification section"""
        verify_frame = self.create_section_frame(parent, "✅ Document Verification", Colors.LIGHT_BLUE)

        # Description with modern typography
        desc_label = tk.Label(verify_frame,
                             text="Verify the authenticity and integrity of digitally signed documents",
                             font=(Fonts.PRIMARY, Fonts.BODY),
                             fg=Colors.TEXT_SECONDARY, bg=Colors.BG_CARD,
                             wraplength=600, justify='center')
        desc_label.pack(pady=(0, 25))

        # Modern file selection container
        file_container = tk.Frame(verify_frame, bg=Colors.BG_CARD)
        file_container.pack(fill='x', pady=10)

        # Original document selection with modern styling
        orig_section = tk.Frame(file_container, bg=Colors.BG_CARD)
        orig_section.pack(fill='x', pady=8)

        orig_label = tk.Label(orig_section, text="Original Document:",
                             font=(Fonts.PRIMARY, Fonts.BODY, 'bold'),
                             fg=Colors.TEXT_PRIMARY, bg=Colors.BG_CARD)
        orig_label.pack(anchor='w')

        orig_row = tk.Frame(orig_section, bg=Colors.BG_CARD)
        orig_row.pack(fill='x', pady=(5, 0))

        self.orig_doc_label = tk.Label(orig_row, text="📄 No document selected",
                                      font=(Fonts.PRIMARY, Fonts.SMALL),
                                      fg=Colors.ERROR, bg=Colors.BG_MAIN,
                                      relief='flat', bd=1, padx=10, pady=8,
                                      anchor='w')
        self.orig_doc_label.pack(side='left', fill='x', expand=True, padx=(0, 10))

        orig_browse_btn = self.create_modern_button(orig_row, "Browse Documents",
                                                   self.select_original_document, Colors.LIGHT_BLUE)
        orig_browse_btn.pack(side='right')

        # Signature file selection with modern styling
        sig_section = tk.Frame(file_container, bg=Colors.BG_CARD)
        sig_section.pack(fill='x', pady=8)

        sig_label = tk.Label(sig_section, text="Signature File (.sig):",
                            font=(Fonts.PRIMARY, Fonts.BODY, 'bold'),
                            fg=Colors.TEXT_PRIMARY, bg=Colors.BG_CARD)
        sig_label.pack(anchor='w')

        sig_row = tk.Frame(sig_section, bg=Colors.BG_CARD)
        sig_row.pack(fill='x', pady=(5, 0))

        self.sig_file_label = tk.Label(sig_row, text="🔏 No signature selected",
                                      font=(Fonts.PRIMARY, Fonts.SMALL),
                                      fg=Colors.ERROR, bg=Colors.BG_MAIN,
                                      relief='flat', bd=1, padx=10, pady=8,
                                      anchor='w')
        self.sig_file_label.pack(side='left', fill='x', expand=True, padx=(0, 10))

        sig_browse_btn = self.create_modern_button(sig_row, "Browse Signatures",
                                                  self.select_signature_file, Colors.LIGHT_BLUE)
        sig_browse_btn.pack(side='right')

        # Certificate file selection with modern styling
        cert_section = tk.Frame(file_container, bg=Colors.BG_CARD)
        cert_section.pack(fill='x', pady=8)

        cert_label = tk.Label(cert_section, text="Certificate File (.pem):",
                             font=(Fonts.PRIMARY, Fonts.BODY, 'bold'),
                             fg=Colors.TEXT_PRIMARY, bg=Colors.BG_CARD)
        cert_label.pack(anchor='w')

        cert_row = tk.Frame(cert_section, bg=Colors.BG_CARD)
        cert_row.pack(fill='x', pady=(5, 0))

        self.cert_verify_label = tk.Label(cert_row, text="📜 No certificate selected",
                                         font=(Fonts.PRIMARY, Fonts.SMALL),
                                         fg=Colors.ERROR, bg=Colors.BG_MAIN,
                                         relief='flat', bd=1, padx=10, pady=8,
                                         anchor='w')
        self.cert_verify_label.pack(side='left', fill='x', expand=True, padx=(0, 10))

        cert_browse_btn = self.create_modern_button(cert_row, "Browse Certificates",
                                                   self.select_certificate_for_verification, Colors.LIGHT_BLUE)
        cert_browse_btn.pack(side='right')

        # Modern verify button
        button_container = tk.Frame(verify_frame, bg=Colors.BG_CARD)
        button_container.pack(pady=25)

        self.verify_button = self.create_modern_button(button_container, "🔍 Verify Document",
                                                      self.verify_document, Colors.LIGHT_BLUE)
        self.verify_button.pack()

        # Status label with modern styling
        self.verify_status_label = tk.Label(verify_frame, text="",
                                           font=(Fonts.PRIMARY, Fonts.BODY),
                                           bg=Colors.BG_CARD, wraplength=600)
        self.verify_status_label.pack(pady=(15, 0))

    def create_status_bar(self):
        """Create modern status bar"""
        # Status container
        status_container = tk.Frame(self.scrollable_frame, bg=Colors.BG_MAIN)
        status_container.pack(fill='x', side='bottom', padx=0, pady=(10, 20))

        # Modern status bar with gradient effect
        status_frame = tk.Frame(status_container, bg=Colors.BG_SIDEBAR, height=40)
        status_frame.pack(fill='x', padx=20)
        status_frame.pack_propagate(False)

        # Left side - Status indicator
        left_status = tk.Frame(status_frame, bg=Colors.BG_SIDEBAR)
        left_status.pack(side='left', fill='y', padx=20, pady=8)

        self.status_label = tk.Label(left_status, text="🟢 System Ready",
                                    font=(Fonts.PRIMARY, Fonts.BODY),
                                    fg=Colors.TEXT_WHITE, bg=Colors.BG_SIDEBAR)
        self.status_label.pack(anchor='w')

        # Right side - System information
        right_status = tk.Frame(status_frame, bg=Colors.BG_SIDEBAR)
        right_status.pack(side='right', fill='y', padx=20, pady=8)

        self.system_info_label = tk.Label(right_status, text="",
                                         font=(Fonts.PRIMARY, Fonts.SMALL),
                                         fg=Colors.TEXT_LIGHT, bg=Colors.BG_SIDEBAR)
        self.system_info_label.pack(anchor='e')

    # ==================== CORE FUNCTIONALITY METHODS ====================

    def register_user(self):
        """Register a new user with RSA key pair and certificate"""
        username = self.username_entry.get().strip()
        if not username:
            messagebox.showerror("Error", "Please enter a username!")
            return

        if not username.isalnum():
            messagebox.showerror("Error", "Username must contain only letters and numbers!")
            return

        # Check if user already exists in database
        existing_user = self.db.get_user_by_username(username)
        if existing_user:
            messagebox.showerror("Error", f"User '{username}' already exists!")
            return

        try:
            self.reg_status_label.config(text="Generating RSA key pair...", fg='#f39c12')
            self.root.update()

            # Generate RSA key pair
            private_key = rsa.generate_private_key(
                public_exponent=65537,
                key_size=2048,
            )
            public_key = private_key.public_key()

            # Create certificate
            self.reg_status_label.config(text="Creating certificate...", fg='#f39c12')
            self.root.update()

            subject = issuer = x509.Name([
                x509.NameAttribute(NameOID.COUNTRY_NAME, "US"),
                x509.NameAttribute(NameOID.STATE_OR_PROVINCE_NAME, "State"),
                x509.NameAttribute(NameOID.LOCALITY_NAME, "City"),
                x509.NameAttribute(NameOID.ORGANIZATION_NAME, "PKI System"),
                x509.NameAttribute(NameOID.COMMON_NAME, username),
            ])

            cert = x509.CertificateBuilder().subject_name(
                subject
            ).issuer_name(
                issuer
            ).public_key(
                public_key
            ).serial_number(
                x509.random_serial_number()
            ).not_valid_before(
                datetime.now(timezone.utc)
            ).not_valid_after(
                datetime.now(timezone.utc) + timedelta(days=365)
            ).sign(private_key, hashes.SHA256())

            # File paths
            private_key_path = f"keys/{username}_private.pem"
            public_key_path = f"keys/{username}_public.pem"
            certificate_path = f"certs/{username}_cert.pem"

            # Save private key
            with open(private_key_path, "wb") as f:
                f.write(private_key.private_bytes(
                    encoding=serialization.Encoding.PEM,
                    format=serialization.PrivateFormat.PKCS8,
                    encryption_algorithm=serialization.NoEncryption()
                ))

            # Save public key
            with open(public_key_path, "wb") as f:
                f.write(public_key.public_bytes(
                    encoding=serialization.Encoding.PEM,
                    format=serialization.PublicFormat.SubjectPublicKeyInfo
                ))

            # Save certificate
            with open(certificate_path, "wb") as f:
                f.write(cert.public_bytes(serialization.Encoding.PEM))

            # Add user to database
            user_id = self.db.add_user(username, private_key_path, public_key_path, certificate_path)
            
            # Add certificate to database
            self.db.add_certificate(
                user_id=user_id,
                serial_number=str(cert.serial_number),
                subject_name=str(subject),
                issuer_name=str(issuer),
                valid_from=cert.not_valid_before_utc,
                valid_until=cert.not_valid_after_utc,
                certificate_path=certificate_path
            )
            
            # Log audit event
            self.db.log_audit_event(user_id, "USER_REGISTERED", f"User {username} registered successfully")

            self.reg_status_label.config(text=f"✓ User '{username}' registered successfully!", fg='#27ae60')
            self.username_entry.delete(0, tk.END)
            self.update_system_status()

            messagebox.showinfo("Success",
                              f"User '{username}' registered successfully!\n\n"
                              f"Files created:\n"
                              f"• {private_key_path}\n"
                              f"• {public_key_path}\n"
                              f"• {certificate_path}")

        except Exception as e:
            self.reg_status_label.config(text=f"Registration failed: {str(e)}", fg='#e74c3c')
            messagebox.showerror("Error", f"Registration failed: {str(e)}")

    def select_private_key(self):
        """Select private key file with modern feedback"""
        file_path = filedialog.askopenfilename(
            title="Select Private Key File",
            filetypes=[("PEM files", "*.pem"), ("All files", "*.*")],
            initialdir="keys" if os.path.exists("keys") else "."
        )
        if file_path:
            self.private_key_path = file_path
            filename = os.path.basename(file_path)
            # Modern success feedback
            self.key_path_label.config(text=f"🔑 {filename}",
                                      fg=Colors.SUCCESS, bg=Colors.BG_MAIN)
            self.animate_selection_feedback(self.key_path_label)

    def select_certificate(self):
        """Select certificate file with modern feedback"""
        file_path = filedialog.askopenfilename(
            title="Select Certificate File",
            filetypes=[("PEM files", "*.pem"), ("All files", "*.*")],
            initialdir="certs" if os.path.exists("certs") else "."
        )
        if file_path:
            self.certificate_path = file_path
            filename = os.path.basename(file_path)
            # Modern success feedback
            self.cert_path_label.config(text=f"📜 {filename}",
                                       fg=Colors.SUCCESS, bg=Colors.BG_MAIN)
            self.animate_selection_feedback(self.cert_path_label)

    def animate_selection_feedback(self, label):
        """Animate file selection feedback"""
        original_bg = label.cget('bg')
        # Brief highlight animation
        label.config(bg=Colors.SUCCESS)
        self.root.after(150, lambda: label.config(bg=original_bg))

    def perform_login(self):
        """Perform automatic user login based on username"""
        # Get username from entry field
        username = self.login_username_entry.get().strip()

        if not username:
            messagebox.showerror("Error", "Please enter your username!")
            return

        try:
            self.login_status_label.config(text="Authenticating...", fg=Colors.WARNING)
            self.root.update()

            # Check if user exists in database
            user_data = self.db.get_user_by_username(username)
            if not user_data:
                messagebox.showerror("Error", f"User '{username}' not found!\nPlease register first or check your username.")
                self.login_status_label.config(text="User not found", fg=Colors.ERROR)
                return

            # Extract user information from database
            user_id, db_username, private_key_path, public_key_path, certificate_path = user_data[:5]

            # Verify files exist
            if not os.path.exists(private_key_path):
                messagebox.showerror("Error", f"Private key file not found: {private_key_path}")
                self.login_status_label.config(text="Private key file missing", fg=Colors.ERROR)
                return

            if not os.path.exists(certificate_path):
                messagebox.showerror("Error", f"Certificate file not found: {certificate_path}")
                self.login_status_label.config(text="Certificate file missing", fg=Colors.ERROR)
                return

            # Update status display
            self.key_status_label.config(text=f"🔑 Private key: {os.path.basename(private_key_path)}",
                                        fg=Colors.SUCCESS)
            self.cert_status_label.config(text=f"📜 Certificate: {os.path.basename(certificate_path)}",
                                         fg=Colors.SUCCESS)
            self.root.update()

            # Test loading the private key to verify it's valid
            with open(private_key_path, "rb") as key_file:
                private_key = load_pem_private_key(key_file.read(), password=None)

            # Test loading the certificate to verify it's valid
            with open(certificate_path, 'rb') as f:
                cert_data = f.read()
            cert = x509.load_pem_x509_certificate(cert_data)

            # Verify the certificate matches the username
            subject = cert.subject
            cert_username = subject.get_attributes_for_oid(NameOID.COMMON_NAME)[0].value
            if cert_username != username:
                messagebox.showerror("Error", f"Certificate username mismatch!\nExpected: {username}\nFound: {cert_username}")
                self.login_status_label.config(text="Certificate mismatch", fg=Colors.ERROR)
                return

            # Set login state
            self.current_user = username
            self.current_user_id = user_id
            self.is_logged_in = True
            self.private_key_path = private_key_path
            self.certificate_path = certificate_path

            # Update last login in database
            self.db.update_last_login(self.current_user_id)

            # Log successful login
            self.db.log_audit_event(self.current_user_id, "USER_LOGIN", f"User {username} logged in successfully")

            # Update header UI
            self.user_status_label.config(text=f"● Logged in as: {username}", fg=Colors.SUCCESS)
            self.user_info_label.config(text="All features enabled")

            # Switch to user-centric UI
            self.create_user_ui()

            self.update_system_status()

            messagebox.showinfo("Login Successful",
                              f"Welcome back, {username}!\n\n"
                              f"✅ Credentials loaded automatically\n"
                              f"✅ Private key: {os.path.basename(private_key_path)}\n"
                              f"✅ Certificate: {os.path.basename(certificate_path)}\n\n"
                              f"You now have access to all signing features.")

        except Exception as e:
            self.login_status_label.config(text=f"Login failed: {str(e)}", fg=Colors.ERROR)
            messagebox.showerror("Error", f"Login failed: {str(e)}")

            # Reset status display
            if hasattr(self, 'key_status_label'):
                self.key_status_label.config(text="🔑 Private key: Not loaded", fg=Colors.TEXT_SECONDARY)
            if hasattr(self, 'cert_status_label'):
                self.cert_status_label.config(text="📜 Certificate: Not loaded", fg=Colors.TEXT_SECONDARY)

    def perform_logout(self):
        """Perform user logout and return to initial UI"""
        # Enhanced logout confirmation
        result = messagebox.askyesno(
            "Confirm Logout",
            f"Are you sure you want to logout?\n\n"
            f"👤 Current user: {self.current_user}\n"
            f"🔐 You will need to login again to access PKI features.\n\n"
            f"Any unsaved work will be lost.",
            icon='question'
        )

        if result:
            try:
                # Log audit event before logout
                if hasattr(self, 'current_user_id') and self.current_user_id:
                    self.db.log_audit_event(self.current_user_id, "USER_LOGOUT",
                                          f"User {self.current_user} logged out successfully")

                # Reset state
                self.current_user = None
                self.current_user_id = None
                self.is_logged_in = False
                self.private_key_path = None
                self.certificate_path = None
                self.current_view = None

                # Update header UI if it exists
                if hasattr(self, 'user_status_label'):
                    self.user_status_label.config(text="● Not logged in", fg=Colors.ERROR)
                if hasattr(self, 'user_info_label'):
                    self.user_info_label.config(text="Please login to access features")

                # Clear content and return to initial UI
                for widget in self.content_frame.winfo_children():
                    widget.destroy()

                # Return to login screen
                self.create_initial_ui()
                self.update_system_status()

                # Success message
                messagebox.showinfo(
                    "Logout Successful",
                    f"✅ You have been logged out successfully!\n\n"
                    f"Thank you for using the PKI Document Signing System.\n"
                    f"Login again to continue using PKI features.",
                    icon='info'
                )

            except Exception as e:
                messagebox.showerror("Logout Error", f"An error occurred during logout: {str(e)}")
                # Force logout anyway for security
                self.current_user = None
                self.is_logged_in = False
                for widget in self.content_frame.winfo_children():
                    widget.destroy()
                self.create_initial_ui()

    def select_document(self):
        """Select document to sign with modern feedback"""
        file_path = filedialog.askopenfilename(
            title="Select Document to Sign",
            filetypes=[("Text files", "*.txt"), ("PDF files", "*.pdf"), ("All files", "*.*")]
        )
        if file_path:
            self.document_path = file_path
            filename = os.path.basename(file_path)
            # Modern success feedback
            self.doc_path_label.config(text=f"📄 {filename}",
                                      fg=Colors.SUCCESS, bg=Colors.BG_MAIN)
            self.animate_selection_feedback(self.doc_path_label)

    def sign_document(self):
        """Sign the selected document with database integration"""
        if not hasattr(self, 'document_path') or not self.document_path:
            messagebox.showerror("Error", "Please select a document to sign!")
            return

        try:
            self.sign_status_label.config(text="Signing document...", fg='#f39c12')
            self.root.update()

            # Read the document
            with open(self.document_path, 'rb') as f:
                document_data = f.read()

            # Calculate document hash for database storage
            document_hash = hashlib.sha256(document_data).hexdigest()

            # Load private key
            with open(self.private_key_path, "rb") as key_file:
                private_key = load_pem_private_key(key_file.read(), password=None)

            # Create signature
            signature = private_key.sign(
                document_data,
                padding.PSS(
                    mgf=padding.MGF1(hashes.SHA256()),
                    salt_length=padding.PSS.MAX_LENGTH
                ),
                hashes.SHA256()
            )

            # Save signed document and signature
            doc_name = os.path.basename(self.document_path)
            signed_doc_path = f"signed_docs/{doc_name}"
            signature_path = f"signed_docs/{doc_name}.sig"
            cert_copy_path = f"signed_docs/{doc_name}_cert.pem"

            # Copy original document to signed_docs
            with open(self.document_path, 'rb') as src, open(signed_doc_path, 'wb') as dst:
                dst.write(src.read())

            # Save signature
            with open(signature_path, 'wb') as f:
                f.write(signature)

            # Copy certificate for verification
            with open(self.certificate_path, 'rb') as src, open(cert_copy_path, 'wb') as dst:
                dst.write(src.read())

            # Add signed document to database
            document_id = self.db.add_signed_document(
                user_id=self.current_user_id,
                document_name=doc_name,
                document_path=signed_doc_path,
                signature_path=signature_path,
                document_hash=document_hash
            )

            # Log audit event
            self.db.log_audit_event(
                self.current_user_id,
                "DOCUMENT_SIGNED",
                f"Document '{doc_name}' signed successfully (ID: {document_id})"
            )

            self.sign_status_label.config(text=f"✓ Document signed successfully!", fg='#27ae60')
            self.update_system_status()

            messagebox.showinfo("Success",
                              f"Document signed successfully!\n\n"
                              f"Files created:\n"
                              f"• {signed_doc_path}\n"
                              f"• {signature_path}\n"
                              f"• {cert_copy_path}\n\n"
                              f"Document ID: {document_id}\n"
                              f"Use these files for verification.")

        except Exception as e:
            self.sign_status_label.config(text=f"Signing failed: {str(e)}", fg='#e74c3c')
            messagebox.showerror("Error", f"Document signing failed: {str(e)}")

            # Log failed signing attempt
            if hasattr(self, 'current_user_id') and self.current_user_id:
                self.db.log_audit_event(
                    self.current_user_id,
                    "DOCUMENT_SIGN_FAILED",
                    f"Failed to sign document: {str(e)}"
                )

    def select_original_document(self):
        """Select original document for verification with modern feedback"""
        file_path = filedialog.askopenfilename(
            title="Select Original Document for Verification",
            filetypes=[("Text files", "*.txt"), ("PDF files", "*.pdf"), ("All files", "*.*")],
            initialdir="signed_docs" if os.path.exists("signed_docs") else "."
        )
        if file_path:
            self.orig_document_path = file_path
            filename = os.path.basename(file_path)
            # Modern success feedback
            self.orig_doc_label.config(text=f"📄 {filename}",
                                      fg=Colors.SUCCESS, bg=Colors.BG_MAIN)
            self.animate_selection_feedback(self.orig_doc_label)

    def select_signature_file(self):
        """Select signature file for verification with modern feedback"""
        file_path = filedialog.askopenfilename(
            title="Select Digital Signature File",
            filetypes=[("Signature files", "*.sig"), ("All files", "*.*")],
            initialdir="signed_docs" if os.path.exists("signed_docs") else "."
        )
        if file_path:
            self.signature_file_path = file_path
            filename = os.path.basename(file_path)
            # Modern success feedback
            self.sig_file_label.config(text=f"🔏 {filename}",
                                      fg=Colors.SUCCESS, bg=Colors.BG_MAIN)
            self.animate_selection_feedback(self.sig_file_label)

    def select_certificate_for_verification(self):
        """Select certificate file for verification with modern feedback"""
        file_path = filedialog.askopenfilename(
            title="Select Certificate File for Verification",
            filetypes=[("PEM files", "*.pem"), ("Certificate files", "*.crt"), ("All files", "*.*")],
            initialdir="signed_docs" if os.path.exists("signed_docs") else "."
        )
        if file_path:
            self.cert_verify_path = file_path
            filename = os.path.basename(file_path)
            # Modern success feedback
            self.cert_verify_label.config(text=f"📜 {filename}",
                                         fg=Colors.SUCCESS, bg=Colors.BG_MAIN)
            self.animate_selection_feedback(self.cert_verify_label)

    def verify_document(self):
        """Verify document signature with database integration"""
        if not hasattr(self, 'orig_document_path') or not self.orig_document_path:
            messagebox.showerror("Error", "Please select the original document!")
            return

        if not hasattr(self, 'signature_file_path') or not self.signature_file_path:
            messagebox.showerror("Error", "Please select the signature file!")
            return

        if not hasattr(self, 'cert_verify_path') or not self.cert_verify_path:
            messagebox.showerror("Error", "Please select the certificate file!")
            return

        try:
            self.verify_status_label.config(text="Verifying document...", fg='#f39c12')
            self.root.update()

            # Read original document
            with open(self.orig_document_path, 'rb') as f:
                document_data = f.read()

            # Calculate document hash for database lookup
            document_hash = hashlib.sha256(document_data).hexdigest()

            # Read signature
            with open(self.signature_file_path, 'rb') as f:
                signature = f.read()

            # Read certificate and extract public key
            with open(self.cert_verify_path, 'rb') as f:
                cert_data = f.read()

            cert = x509.load_pem_x509_certificate(cert_data)
            public_key = cert.public_key()

            # Get certificate info for logging
            subject = cert.subject
            common_name = subject.get_attributes_for_oid(NameOID.COMMON_NAME)[0].value

            # Prepare verification details
            verification_details = {
                'document_path': self.orig_document_path,
                'signature_path': self.signature_file_path,
                'certificate_path': self.cert_verify_path,
                'document_hash': document_hash,
                'signer_cn': common_name,
                'verification_time': datetime.now().isoformat()
            }

            # Verify signature
            verification_result = False
            try:
                public_key.verify(
                    signature,
                    document_data,
                    padding.PSS(
                        mgf=padding.MGF1(hashes.SHA256()),
                        salt_length=padding.PSS.MAX_LENGTH
                    ),
                    hashes.SHA256()
                )
                verification_result = True

                self.verify_status_label.config(text="✓ Document signature is VALID!", fg='#27ae60')

                # Try to find the document in database and update verification count
                document_id = self.db.find_document_by_hash(document_hash)
                if document_id:
                    self.db.update_document_verification(document_id)
                    verification_details['document_id'] = document_id

                # Log successful verification
                self.db.add_verification_log(
                    document_id=document_id,
                    verifier_info=f"User: {self.current_user if self.current_user else 'Anonymous'}",
                    verification_result=True,
                    verification_details=json.dumps(verification_details)
                )

                messagebox.showinfo("Verification Successful",
                                  f"✓ Document signature is VALID!\n\n"
                                  f"Signed by: {common_name}\n"
                                  f"Document integrity: VERIFIED\n"
                                  f"Signature authenticity: CONFIRMED\n"
                                  f"Document Hash: {document_hash[:16]}...")

            except Exception as verify_error:
                verification_result = False
                verification_details['error'] = str(verify_error)

                self.verify_status_label.config(text="✗ Document signature is INVALID!", fg='#e74c3c')

                # Log failed verification
                self.db.add_verification_log(
                    document_id=None,
                    verifier_info=f"User: {self.current_user if self.current_user else 'Anonymous'}",
                    verification_result=False,
                    verification_details=json.dumps(verification_details)
                )

                messagebox.showerror("Verification Failed",
                                   "✗ Document signature is INVALID!\n\n"
                                   "The document may have been:\n"
                                   "• Modified after signing\n"
                                   "• Signed with a different certificate\n"
                                   "• Corrupted during transfer")

            # Log audit event
            if hasattr(self, 'current_user_id') and self.current_user_id:
                self.db.log_audit_event(
                    self.current_user_id,
                    "DOCUMENT_VERIFIED",
                    f"Document verification {'successful' if verification_result else 'failed'}: {common_name}"
                )

        except Exception as e:
            self.verify_status_label.config(text=f"Verification error: {str(e)}", fg='#e74c3c')
            messagebox.showerror("Error", f"Verification failed: {str(e)}")

            # Log verification error
            if hasattr(self, 'current_user_id') and self.current_user_id:
                self.db.log_audit_event(
                    self.current_user_id,
                    "DOCUMENT_VERIFY_ERROR",
                    f"Verification error: {str(e)}"
                )

    def update_system_status(self):
        """Update system status information"""
        try:
            # Get statistics from database
            stats = self.db.get_user_statistics()
            
            info_parts = []
            info_parts.append(f"Users: {stats['total_users']}")
            info_parts.append(f"Signed Docs: {stats['total_documents']}")
            info_parts.append(f"Verifications: {stats['total_verifications']}")

            # Add current user
            if self.is_logged_in:
                info_parts.append(f"Current: {self.current_user}")

            self.system_info_label.config(text=" | ".join(info_parts))
            
        except Exception as e:
            # Fallback to file-based counting if database fails
            info_parts = []
            
            if os.path.exists('keys'):
                key_count = len([f for f in os.listdir('keys') if f.endswith('_private.pem')])
                info_parts.append(f"Users: {key_count}")
            else:
                info_parts.append("Users: 0")

            if os.path.exists('signed_docs'):
                doc_count = len([f for f in os.listdir('signed_docs')
                               if not f.endswith('.sig') and not f.endswith('_cert.pem')])
                info_parts.append(f"Signed Docs: {doc_count}")
            else:
                info_parts.append("Signed Docs: 0")

            if self.is_logged_in:
                info_parts.append(f"Current: {self.current_user}")

            self.system_info_label.config(text=" | ".join(info_parts))



def main():
    """Main function to run the PKI system"""
    root = tk.Tk()
    app = PKISystem(root)
    root.mainloop()


if __name__ == "__main__":
    main()






