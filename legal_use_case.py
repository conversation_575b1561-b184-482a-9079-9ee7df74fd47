import tkinter as tk
from tkinter import scrolledtext
import tkinter.font as tkFont


class LegalUseCaseWindow:
    def __init__(self, root):
        self.root = root
        self.setup_ui()
        
    def setup_ui(self):
        """Setup the user interface"""
        self.root.title("Use Case - Legal Document Signing")
        self.root.geometry("800x600")
        self.root.resizable(True, True)
        
        # Center the window
        self.center_window()
        
        # Configure colors and fonts
        bg_color = '#f8f9fa'
        text_color = '#2c3e50'
        header_color = '#34495e'
        
        self.root.configure(bg=bg_color)
        
        # Main frame with padding
        main_frame = tk.Frame(self.root, bg=bg_color, padx=30, pady=20)
        main_frame.pack(expand=True, fill='both')
        
        # Title frame
        title_frame = tk.Frame(main_frame, bg=bg_color)
        title_frame.pack(fill='x', pady=(0, 20))
        
        # Main title
        title_font = tkFont.Font(family='Arial', size=18, weight='bold')
        title_label = tk.Label(title_frame, 
                              text="Use Case - Legal Document Signing",
                              font=title_font,
                              fg=header_color,
                              bg=bg_color)
        title_label.pack()
        
        # Subtitle
        subtitle_font = tkFont.Font(family='Arial', size=14, weight='bold')
        subtitle_label = tk.Label(title_frame,
                                 text="Digital Contract Signing System",
                                 font=subtitle_font,
                                 fg='#e74c3c',
                                 bg=bg_color)
        subtitle_label.pack(pady=(10, 0))
        
        # Scrollable text area
        text_frame = tk.Frame(main_frame, bg=bg_color)
        text_frame.pack(expand=True, fill='both', pady=(0, 20))
        
        # Create scrolled text widget
        text_font = tkFont.Font(family='Arial', size=11)
        self.text_area = scrolledtext.ScrolledText(
            text_frame,
            wrap=tk.WORD,
            font=text_font,
            bg='white',
            fg=text_color,
            relief='solid',
            borderwidth=1,
            padx=20,
            pady=15,
            selectbackground='#3498db',
            selectforeground='white'
        )
        self.text_area.pack(expand=True, fill='both')
        
        # Insert the formatted content
        self.insert_content()
        
        # Make text read-only
        self.text_area.config(state='disabled')
        
        # Button frame
        button_frame = tk.Frame(main_frame, bg=bg_color)
        button_frame.pack(fill='x')
        
        # Close button
        close_button = tk.Button(button_frame,
                               text="Close",
                               command=self.root.destroy,
                               bg='#95a5a6',
                               fg='white',
                               font=tkFont.Font(family='Arial', size=10),
                               padx=20,
                               pady=5,
                               relief='flat',
                               cursor='hand2')
        close_button.pack(side='right')
        
        # Demo button
        demo_button = tk.Button(button_frame,
                              text="Open Demo System",
                              command=self.open_demo,
                              bg='#3498db',
                              fg='white',
                              font=tkFont.Font(family='Arial', size=10),
                              padx=20,
                              pady=5,
                              relief='flat',
                              cursor='hand2')
        demo_button.pack(side='right', padx=(0, 10))
    
    def center_window(self):
        """Center the window on the screen"""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f'{width}x{height}+{x}+{y}')
    
    def insert_content(self):
        """Insert the formatted content into the text area"""
        content = """OVERVIEW

This PKI-based application revolutionizes the way legal documents are signed and verified, providing a secure, efficient, and legally compliant alternative to traditional paper-based signatures. The system is designed for contracts, agreements, NDAs, and other legal documents that require authenticated signatures.

KEY FEATURES & BENEFITS

🔐 SECURE USER REGISTRATION
Each user undergoes a secure registration process where they receive a unique digital certificate tied to their identity. This certificate serves as their digital identity and cannot be forged or duplicated.

🖊️ AUTHENTICATED DOCUMENT SIGNING
• Only the registered user can sign documents using their private key
• The signing process creates a unique digital fingerprint for each document
• Private keys are securely stored and never transmitted over networks
• Each signature is mathematically linked to both the signer and the document content

🛡️ TAMPER-PROOF INTEGRITY
• Signed documents cannot be altered without invalidating the signature
• Any modification to the document content is immediately detectable
• The system uses SHA-256 hashing to ensure document integrity
• RSA-PSS digital signatures provide cryptographic proof of authenticity

✅ IDENTITY AUTHENTICATION & VERIFICATION
• The signer's identity is authenticated using their digital certificate
• Certificates contain verified information about the signer
• Public key cryptography ensures non-repudiation
• Authentication prevents unauthorized document signing
• Signers cannot deny having signed a document

🔍 COMPREHENSIVE VERIFICATION
Document verification ensures:
• Integrity: The document has not been modified since signing
• Authenticity: The signature was created by the claimed signer
• Legal Validity: The signature meets digital signature standards
• Non-repudiation: The signer cannot deny their signature

REAL-WORLD APPLICATIONS

🏢 BUSINESS CONTRACTS
• Employment agreements and contracts
• Vendor and supplier agreements
• Partnership and joint venture documents
• Service level agreements (SLAs)
• Non-disclosure agreements (NDAs)

🏛️ GOVERNMENT & LEGAL
• Legal briefs and court documents
• Government forms and applications
• Regulatory compliance documents
• Official correspondence and notices
• Public records and certifications

⚖️ LEGAL FIRMS
• Client agreements and retainers
• Legal opinions and advice letters
• Settlement agreements
• Power of attorney documents
• Wills and estate planning documents

TECHNICAL ADVANTAGES

🔧 CRYPTOGRAPHIC SECURITY
• 2048-bit RSA encryption for maximum security
• SHA-256 hashing algorithm for document integrity
• RSA-PSS signature scheme with optimal security parameters
• X.509 certificate standard for interoperability

💻 USER-FRIENDLY INTERFACE
• Intuitive GUI applications for all operations
• Step-by-step workflow guidance
• Real-time verification feedback
• Professional document management

📊 AUDIT TRAIL
• Complete record of all signing activities
• Timestamp information for legal compliance
• Certificate validity tracking
• Comprehensive verification logs

LEGAL COMPLIANCE

This system meets the requirements for digital signatures under various legal frameworks:
• Electronic Signatures in Global and National Commerce Act (E-SIGN)
• Uniform Electronic Transactions Act (UETA)
• European Union eIDAS Regulation
• Digital signature laws in various jurisdictions

IMPLEMENTATION BENEFITS

💰 COST REDUCTION
• Eliminates paper, printing, and mailing costs
• Reduces document processing time
• Minimizes storage and archival expenses
• Decreases administrative overhead

⚡ EFFICIENCY GAINS
• Instant document signing and verification
• Remote signing capabilities
• Automated workflow processes
• Reduced turnaround times

🌍 ENVIRONMENTAL IMPACT
• Paperless document processing
• Reduced carbon footprint
• Sustainable business practices
• Green technology adoption

SECURITY GUARANTEES

The system provides enterprise-grade security through:
• Military-grade encryption algorithms
• Secure key generation and storage
• Protected certificate management
• Tamper-evident document handling
• Comprehensive audit logging

CONCLUSION

This Digital Contract Signing System represents the future of legal document processing, combining robust security, legal compliance, and user convenience. It enables organizations to modernize their document workflows while maintaining the highest standards of security and legal validity.

The system is suitable for any organization that needs to sign and verify legal documents securely, efficiently, and in compliance with digital signature regulations."""

        # Enable text area for insertion
        self.text_area.config(state='normal')
        
        # Insert content
        self.text_area.insert('1.0', content)
        
        # Apply some basic formatting by finding and styling headers
        self.format_headers()
        
        # Disable text area again
        self.text_area.config(state='disabled')
    
    def format_headers(self):
        """Apply basic formatting to headers in the text"""
        # Configure tags for different text styles
        self.text_area.tag_configure('header', font=('Arial', 12, 'bold'), foreground='#2c3e50')
        self.text_area.tag_configure('subheader', font=('Arial', 11, 'bold'), foreground='#34495e')
        self.text_area.tag_configure('bullet', foreground='#e74c3c')
        
        # Find and tag headers (lines in ALL CAPS)
        content = self.text_area.get('1.0', 'end')
        lines = content.split('\n')
        
        current_line = 1
        for line in lines:
            line_start = f"{current_line}.0"
            line_end = f"{current_line}.end"
            
            # Style main headers (all caps lines)
            if line.strip() and line.strip().isupper() and not line.startswith('•'):
                self.text_area.tag_add('header', line_start, line_end)
            
            # Style bullet points
            elif line.strip().startswith('•'):
                bullet_end = f"{current_line}.1"
                self.text_area.tag_add('bullet', line_start, bullet_end)
            
            # Style emoji headers
            elif any(emoji in line for emoji in ['🔐', '🖊️', '🛡️', '✅', '🔍', '🏢', '🏛️', '⚖️', '🔧', '💻', '📊', '💰', '⚡', '🌍']):
                self.text_area.tag_add('subheader', line_start, line_end)
            
            current_line += 1
    
    def open_demo(self):
        """Open the demo system"""
        try:
            import subprocess
            import sys
            subprocess.Popen([sys.executable, 'demo_pki_system.py'])
        except Exception as e:
            import tkinter.messagebox as messagebox
            messagebox.showerror("Error", f"Failed to open demo system: {str(e)}")


def show_legal_use_case():
    """Main function to display the legal use case window"""
    root = tk.Tk()
    app = LegalUseCaseWindow(root)
    root.mainloop()


if __name__ == "__main__":
    show_legal_use_case()
