#!/usr/bin/env python3
"""
Test script for PKI Registration functionality
"""

import os
import shutil
from cryptography.hazmat.primitives import serialization
from cryptography import x509
from pki_registration import PKIRegistrationApp
import tkinter as tk


def test_key_generation():
    """Test RSA key pair generation"""
    print("Testing RSA key pair generation...")
    
    # Create a temporary app instance for testing
    root = tk.Tk()
    root.withdraw()  # Hide the window
    app = PKIRegistrationApp(root)
    
    # Test key generation
    private_key, public_key = app.generate_rsa_keypair()
    
    # Verify key properties
    assert private_key.key_size == 2048, "Private key should be 2048 bits"
    assert public_key.key_size == 2048, "Public key should be 2048 bits"
    
    print("✓ RSA key pair generation test passed")
    root.destroy()


def test_file_operations():
    """Test file saving operations"""
    print("Testing file operations...")
    
    # Clean up any existing test files
    test_username = "testuser"
    cleanup_test_files(test_username)
    
    # Create a temporary app instance
    root = tk.Tk()
    root.withdraw()
    app = PKIRegistrationApp(root)
    
    # Create directories
    app.create_directories()
    
    # Generate keys
    private_key, public_key = app.generate_rsa_keypair()
    
    # Save keys
    app.save_private_key(private_key, test_username)
    app.save_public_key(public_key, test_username)
    
    # Generate and save certificate
    certificate = app.generate_self_signed_certificate(private_key, public_key, test_username)
    app.save_certificate(certificate, test_username)
    
    # Verify files exist
    assert os.path.exists(f'keys/{test_username}_private.pem'), "Private key file should exist"
    assert os.path.exists(f'keys/{test_username}_public.pem'), "Public key file should exist"
    assert os.path.exists(f'certs/{test_username}_cert.pem'), "Certificate file should exist"
    
    # Verify file contents can be loaded
    with open(f'keys/{test_username}_private.pem', 'rb') as f:
        loaded_private_key = serialization.load_pem_private_key(f.read(), password=None)
        assert loaded_private_key.key_size == 2048, "Loaded private key should be 2048 bits"
    
    with open(f'keys/{test_username}_public.pem', 'rb') as f:
        loaded_public_key = serialization.load_pem_public_key(f.read())
        assert loaded_public_key.key_size == 2048, "Loaded public key should be 2048 bits"
    
    with open(f'certs/{test_username}_cert.pem', 'rb') as f:
        loaded_cert = x509.load_pem_x509_certificate(f.read())
        assert loaded_cert.subject.get_attributes_for_oid(x509.NameOID.COMMON_NAME)[0].value == test_username
    
    print("✓ File operations test passed")
    
    # Clean up
    cleanup_test_files(test_username)
    root.destroy()


def cleanup_test_files(username):
    """Clean up test files"""
    files_to_remove = [
        f'keys/{username}_private.pem',
        f'keys/{username}_public.pem',
        f'certs/{username}_cert.pem'
    ]
    
    for file_path in files_to_remove:
        if os.path.exists(file_path):
            os.remove(file_path)
    
    # Remove directories if empty
    for dir_path in ['keys', 'certs']:
        if os.path.exists(dir_path) and not os.listdir(dir_path):
            os.rmdir(dir_path)


def main():
    """Run all tests"""
    print("Running PKI Registration tests...\n")
    
    try:
        test_key_generation()
        test_file_operations()
        print("\n✓ All tests passed successfully!")
        
    except Exception as e:
        print(f"\n✗ Test failed: {e}")
        return 1
    
    return 0


if __name__ == "__main__":
    exit(main())
