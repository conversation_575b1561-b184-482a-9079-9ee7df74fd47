import tkinter as tk
from tkinter import filedialog, messagebox
import os
import secrets
import string
import threading
import time
from cryptography.hazmat.primitives import serialization, hashes
from cryptography.hazmat.primitives.asymmetric import padding
from cryptography import x509


class PKILoginApp:
    def __init__(self, root):
        self.root = root
        self.private_key_path = None
        self.certificate_path = None
        self.setup_ui()
        
    def setup_ui(self):
        """Setup the user interface"""
        self.root.title("PKI Document Signer - Login")
        self.root.geometry("500x300")
        self.root.resizable(False, False)
        
        # Center the window
        self.center_window()
        
        # Main frame
        main_frame = tk.Frame(self.root, padx=30, pady=20)
        main_frame.pack(expand=True, fill='both')
        
        # Title label
        title_label = tk.Label(main_frame, text="PKI Document Signer - Login", 
                              font=('Arial', 16, 'bold'), fg='#2c3e50')
        title_label.pack(pady=(0, 20))
        
        # Private key selection
        private_key_frame = tk.Frame(main_frame)
        private_key_frame.pack(fill='x', pady=(0, 10))
        
        tk.Label(private_key_frame, text="Private Key:", 
                font=('Arial', 10, 'bold')).pack(anchor='w')
        
        private_key_button_frame = tk.Frame(private_key_frame)
        private_key_button_frame.pack(fill='x', pady=(5, 0))
        
        self.private_key_button = tk.Button(private_key_button_frame, 
                                          text="Select Private Key (.pem)",
                                          command=self.select_private_key,
                                          bg='#3498db', fg='white',
                                          font=('Arial', 10),
                                          padx=10, pady=5)
        self.private_key_button.pack(side='left')
        
        self.private_key_label = tk.Label(private_key_button_frame, 
                                        text="No file selected",
                                        font=('Arial', 9), fg='#7f8c8d')
        self.private_key_label.pack(side='left', padx=(10, 0))
        
        # Certificate selection
        cert_frame = tk.Frame(main_frame)
        cert_frame.pack(fill='x', pady=(0, 20))
        
        tk.Label(cert_frame, text="Certificate:", 
                font=('Arial', 10, 'bold')).pack(anchor='w')
        
        cert_button_frame = tk.Frame(cert_frame)
        cert_button_frame.pack(fill='x', pady=(5, 0))
        
        self.cert_button = tk.Button(cert_button_frame, 
                                   text="Select Certificate (.pem)",
                                   command=self.select_certificate,
                                   bg='#3498db', fg='white',
                                   font=('Arial', 10),
                                   padx=10, pady=5)
        self.cert_button.pack(side='left')
        
        self.cert_label = tk.Label(cert_button_frame, 
                                 text="No file selected",
                                 font=('Arial', 9), fg='#7f8c8d')
        self.cert_label.pack(side='left', padx=(10, 0))
        
        # Login button
        self.login_button = tk.Button(main_frame, text="Login",
                                    command=self.perform_login,
                                    bg='#27ae60', fg='white',
                                    font=('Arial', 12, 'bold'),
                                    padx=30, pady=10)
        self.login_button.pack(pady=(10, 20))
        
        # Result label
        self.result_label = tk.Label(main_frame, text="",
                                   font=('Arial', 12, 'bold'),
                                   height=2)
        self.result_label.pack()
    
    def center_window(self):
        """Center the window on the screen"""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f'{width}x{height}+{x}+{y}')
    
    def select_private_key(self):
        """Handle private key file selection"""
        file_path = filedialog.askopenfilename(
            title="Select Private Key File",
            filetypes=[("PEM files", "*.pem"), ("All files", "*.*")],
            initialdir="keys" if os.path.exists("keys") else "."
        )
        
        if file_path:
            self.private_key_path = file_path
            filename = os.path.basename(file_path)
            self.private_key_label.config(text=filename, fg='#27ae60')
    
    def select_certificate(self):
        """Handle certificate file selection"""
        file_path = filedialog.askopenfilename(
            title="Select Certificate File",
            filetypes=[("PEM files", "*.pem"), ("All files", "*.*")],
            initialdir="certs" if os.path.exists("certs") else "."
        )
        
        if file_path:
            self.certificate_path = file_path
            filename = os.path.basename(file_path)
            self.cert_label.config(text=filename, fg='#27ae60')
    
    def generate_challenge(self):
        """Generate a random 32-character challenge string"""
        alphabet = string.ascii_letters + string.digits
        return ''.join(secrets.choice(alphabet) for _ in range(32))
    
    def load_private_key(self, file_path):
        """Load private key from file"""
        try:
            with open(file_path, 'rb') as f:
                private_key = serialization.load_pem_private_key(
                    f.read(),
                    password=None
                )
            return private_key
        except Exception as e:
            raise Exception(f"Failed to load private key: {str(e)}")
    
    def load_certificate(self, file_path):
        """Load certificate from file and extract public key"""
        try:
            with open(file_path, 'rb') as f:
                certificate = x509.load_pem_x509_certificate(f.read())
            public_key = certificate.public_key()
            return certificate, public_key
        except Exception as e:
            raise Exception(f"Failed to load certificate: {str(e)}")
    
    def sign_challenge(self, private_key, challenge):
        """Sign the challenge using the private key"""
        try:
            signature = private_key.sign(
                challenge.encode('utf-8'),
                padding.PSS(
                    mgf=padding.MGF1(hashes.SHA256()),
                    salt_length=padding.PSS.MAX_LENGTH
                ),
                hashes.SHA256()
            )
            return signature
        except Exception as e:
            raise Exception(f"Failed to sign challenge: {str(e)}")
    
    def verify_signature(self, public_key, challenge, signature):
        """Verify the signature using the public key"""
        try:
            public_key.verify(
                signature,
                challenge.encode('utf-8'),
                padding.PSS(
                    mgf=padding.MGF1(hashes.SHA256()),
                    salt_length=padding.PSS.MAX_LENGTH
                ),
                hashes.SHA256()
            )
            return True
        except Exception:
            return False
    
    def animate_result(self, text, color, success=True):
        """Animate the result label with fade-in effect"""
        self.result_label.config(text=text, fg=color)
        
        if success:
            # Fade-in animation for success
            def fade_in():
                for alpha in range(0, 101, 5):
                    # Simulate fade effect by changing text color intensity
                    fade_color = self.interpolate_color('#ffffff', color, alpha / 100)
                    self.result_label.config(fg=fade_color)
                    time.sleep(0.03)
                self.result_label.config(fg=color)
            
            threading.Thread(target=fade_in, daemon=True).start()
        else:
            # Simple blink effect for failure
            def blink():
                for _ in range(3):
                    self.result_label.config(fg=color)
                    time.sleep(0.2)
                    self.result_label.config(fg='#ffffff')
                    time.sleep(0.2)
                self.result_label.config(fg=color)
            
            threading.Thread(target=blink, daemon=True).start()
    
    def interpolate_color(self, color1, color2, factor):
        """Interpolate between two hex colors"""
        # Simple color interpolation for fade effect
        if factor <= 0:
            return color1
        elif factor >= 1:
            return color2
        else:
            # For simplicity, just return the target color
            return color2
    
    def perform_login(self):
        """Perform the PKI authentication process"""
        # Validate file selections
        if not self.private_key_path:
            messagebox.showerror("Error", "Please select a private key file!")
            return
        
        if not self.certificate_path:
            messagebox.showerror("Error", "Please select a certificate file!")
            return
        
        # Disable login button during processing
        self.login_button.config(state='disabled', text='Authenticating...')
        self.result_label.config(text="", fg='black')
        
        def authentication_process():
            try:
                # Step 1: Load certificate and extract public key
                certificate, public_key = self.load_certificate(self.certificate_path)
                
                # Step 2: Load private key
                private_key = self.load_private_key(self.private_key_path)
                
                # Step 3: Generate random challenge
                challenge = self.generate_challenge()
                
                # Step 4: Sign the challenge using private key
                signature = self.sign_challenge(private_key, challenge)
                
                # Step 5: Verify signature using public key from certificate
                verification_result = self.verify_signature(public_key, challenge, signature)
                
                # Step 6 & 7: Display result
                if verification_result:
                    self.animate_result("Login Successful", "#27ae60", success=True)
                else:
                    self.animate_result("Authentication Failed", "#e74c3c", success=False)
                    
            except Exception as e:
                messagebox.showerror("Authentication Error", str(e))
                self.result_label.config(text="Authentication Error", fg="#e74c3c")
            
            finally:
                # Re-enable login button
                self.login_button.config(state='normal', text='Login')
        
        # Run authentication in separate thread to prevent GUI freezing
        threading.Thread(target=authentication_process, daemon=True).start()


def login_gui():
    """Main function to create and run the login GUI"""
    root = tk.Tk()
    app = PKILoginApp(root)
    root.mainloop()


if __name__ == "__main__":
    login_gui()
