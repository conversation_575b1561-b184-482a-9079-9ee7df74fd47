import tkinter as tk
from tkinter import filedialog, messagebox
import os
import hashlib
import shutil
import threading
import time
from cryptography.hazmat.primitives import serialization, hashes
from cryptography.hazmat.primitives.asymmetric import padding


class PKIDocumentSignerApp:
    def __init__(self, root):
        self.root = root
        self.document_path = None
        self.private_key_path = None
        self.certificate_path = None
        self.setup_ui()
        
    def setup_ui(self):
        """Setup the user interface"""
        self.root.title("PKI Document Signer - Document Signing")
        self.root.geometry("600x400")
        self.root.resizable(False, False)
        
        # Center the window
        self.center_window()
        
        # Main frame
        main_frame = tk.Frame(self.root, padx=30, pady=20)
        main_frame.pack(expand=True, fill='both')
        
        # Title label
        title_label = tk.Label(main_frame, 
                              text="PKI Document Signer - Document Signing", 
                              font=('Arial', 16, 'bold'), 
                              fg='#2c3e50')
        title_label.pack(pady=(0, 25))
        
        # Document file selection
        doc_frame = tk.Frame(main_frame)
        doc_frame.pack(fill='x', pady=(0, 15))
        
        tk.Label(doc_frame, text="Document to Sign:", 
                font=('Arial', 10, 'bold')).pack(anchor='w')
        
        doc_button_frame = tk.Frame(doc_frame)
        doc_button_frame.pack(fill='x', pady=(5, 0))
        
        self.doc_button = tk.Button(doc_button_frame, 
                                   text="Select Document (.txt, .pdf, etc.)",
                                   command=self.select_document,
                                   bg='#e74c3c', fg='white',
                                   font=('Arial', 10),
                                   padx=10, pady=5)
        self.doc_button.pack(side='left')
        
        self.doc_label = tk.Label(doc_button_frame, 
                                 text="No file selected",
                                 font=('Arial', 9), fg='#7f8c8d')
        self.doc_label.pack(side='left', padx=(10, 0))
        
        # Private key selection
        private_key_frame = tk.Frame(main_frame)
        private_key_frame.pack(fill='x', pady=(0, 15))
        
        tk.Label(private_key_frame, text="Private Key:", 
                font=('Arial', 10, 'bold')).pack(anchor='w')
        
        private_key_button_frame = tk.Frame(private_key_frame)
        private_key_button_frame.pack(fill='x', pady=(5, 0))
        
        self.private_key_button = tk.Button(private_key_button_frame, 
                                          text="Select Private Key (.pem)",
                                          command=self.select_private_key,
                                          bg='#3498db', fg='white',
                                          font=('Arial', 10),
                                          padx=10, pady=5)
        self.private_key_button.pack(side='left')
        
        self.private_key_label = tk.Label(private_key_button_frame, 
                                        text="No file selected",
                                        font=('Arial', 9), fg='#7f8c8d')
        self.private_key_label.pack(side='left', padx=(10, 0))
        
        # Certificate selection
        cert_frame = tk.Frame(main_frame)
        cert_frame.pack(fill='x', pady=(0, 25))
        
        tk.Label(cert_frame, text="Certificate:", 
                font=('Arial', 10, 'bold')).pack(anchor='w')
        
        cert_button_frame = tk.Frame(cert_frame)
        cert_button_frame.pack(fill='x', pady=(5, 0))
        
        self.cert_button = tk.Button(cert_button_frame, 
                                   text="Select Certificate (.pem)",
                                   command=self.select_certificate,
                                   bg='#3498db', fg='white',
                                   font=('Arial', 10),
                                   padx=10, pady=5)
        self.cert_button.pack(side='left')
        
        self.cert_label = tk.Label(cert_button_frame, 
                                 text="No file selected",
                                 font=('Arial', 9), fg='#7f8c8d')
        self.cert_label.pack(side='left', padx=(10, 0))
        
        # Sign button
        self.sign_button = tk.Button(main_frame, text="Sign Document",
                                   command=self.sign_document,
                                   bg='#27ae60', fg='white',
                                   font=('Arial', 12, 'bold'),
                                   padx=30, pady=10)
        self.sign_button.pack(pady=(15, 20))
        
        # Result label
        self.result_label = tk.Label(main_frame, text="",
                                   font=('Arial', 12, 'bold'),
                                   height=2)
        self.result_label.pack()
    
    def center_window(self):
        """Center the window on the screen"""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f'{width}x{height}+{x}+{y}')
    
    def select_document(self):
        """Handle document file selection"""
        file_path = filedialog.askopenfilename(
            title="Select Document to Sign",
            filetypes=[
                ("Text files", "*.txt"),
                ("PDF files", "*.pdf"),
                ("Word documents", "*.doc *.docx"),
                ("All files", "*.*")
            ]
        )
        
        if file_path:
            self.document_path = file_path
            filename = os.path.basename(file_path)
            self.doc_label.config(text=filename, fg='#27ae60')
    
    def select_private_key(self):
        """Handle private key file selection"""
        file_path = filedialog.askopenfilename(
            title="Select Private Key File",
            filetypes=[("PEM files", "*.pem"), ("All files", "*.*")],
            initialdir="keys" if os.path.exists("keys") else "."
        )
        
        if file_path:
            self.private_key_path = file_path
            filename = os.path.basename(file_path)
            self.private_key_label.config(text=filename, fg='#27ae60')
    
    def select_certificate(self):
        """Handle certificate file selection"""
        file_path = filedialog.askopenfilename(
            title="Select Certificate File",
            filetypes=[("PEM files", "*.pem"), ("All files", "*.*")],
            initialdir="certs" if os.path.exists("certs") else "."
        )
        
        if file_path:
            self.certificate_path = file_path
            filename = os.path.basename(file_path)
            self.cert_label.config(text=filename, fg='#27ae60')
    
    def read_document_content(self, file_path):
        """Read document content as bytes"""
        try:
            with open(file_path, 'rb') as f:
                return f.read()
        except Exception as e:
            raise Exception(f"Failed to read document: {str(e)}")
    
    def hash_content(self, content):
        """Hash content using SHA-256"""
        return hashlib.sha256(content).digest()
    
    def load_private_key(self, file_path):
        """Load private key from file"""
        try:
            with open(file_path, 'rb') as f:
                private_key = serialization.load_pem_private_key(
                    f.read(),
                    password=None
                )
            return private_key
        except Exception as e:
            raise Exception(f"Failed to load private key: {str(e)}")
    
    def sign_hash(self, private_key, hash_value):
        """Sign the hash using the private key"""
        try:
            signature = private_key.sign(
                hash_value,
                padding.PSS(
                    mgf=padding.MGF1(hashes.SHA256()),
                    salt_length=padding.PSS.MAX_LENGTH
                ),
                hashes.SHA256()
            )
            return signature
        except Exception as e:
            raise Exception(f"Failed to sign document: {str(e)}")
    
    def create_signed_docs_folder(self):
        """Create signed_docs folder if it doesn't exist"""
        os.makedirs('signed_docs', exist_ok=True)
    
    def save_signed_files(self, document_path, signature, certificate_path):
        """Save the signed document files"""
        try:
            # Get filename without extension
            filename = os.path.basename(document_path)
            name_without_ext = os.path.splitext(filename)[0]
            
            # Create signed_docs folder
            self.create_signed_docs_folder()
            
            # Copy original document to signed_docs folder
            signed_doc_path = os.path.join('signed_docs', filename)
            shutil.copy2(document_path, signed_doc_path)
            
            # Save signature
            signature_path = os.path.join('signed_docs', f'{name_without_ext}.sig')
            with open(signature_path, 'wb') as f:
                f.write(signature)
            
            # Copy certificate
            cert_dest_path = os.path.join('signed_docs', f'{name_without_ext}_cert.pem')
            shutil.copy2(certificate_path, cert_dest_path)
            
            return signed_doc_path, signature_path, cert_dest_path
            
        except Exception as e:
            raise Exception(f"Failed to save signed files: {str(e)}")
    
    def animate_success(self):
        """Animate the success message with fade-in effect"""
        self.result_label.config(text="Document signed successfully!", fg='#27ae60')
        
        def fade_in():
            # Simple fade-in simulation by changing opacity-like effect
            colors = ['#ffffff', '#e8f5e8', '#d4edda', '#c3e6cb', '#b8dcc6', '#27ae60']
            for color in colors:
                self.result_label.config(fg=color)
                time.sleep(0.1)
            self.result_label.config(fg='#27ae60')
        
        threading.Thread(target=fade_in, daemon=True).start()
    
    def sign_document(self):
        """Perform the document signing process"""
        # Validate file selections
        if not self.document_path:
            messagebox.showerror("Error", "Please select a document to sign!")
            return
        
        if not self.private_key_path:
            messagebox.showerror("Error", "Please select a private key file!")
            return
        
        if not self.certificate_path:
            messagebox.showerror("Error", "Please select a certificate file!")
            return
        
        # Disable sign button during processing
        self.sign_button.config(state='disabled', text='Signing...')
        self.result_label.config(text="", fg='black')
        
        def signing_process():
            try:
                # Step 1: Read the document file
                document_content = self.read_document_content(self.document_path)
                
                # Step 2: Hash the content using SHA-256
                document_hash = self.hash_content(document_content)
                
                # Step 3: Load private key and sign the hash
                private_key = self.load_private_key(self.private_key_path)
                signature = self.sign_hash(private_key, document_hash)
                
                # Step 4: Save all files to signed_docs folder
                signed_paths = self.save_signed_files(
                    self.document_path, 
                    signature, 
                    self.certificate_path
                )
                
                # Step 5: Show success message with animation
                self.animate_success()
                
                # Show info about saved files
                filename = os.path.basename(self.document_path)
                name_without_ext = os.path.splitext(filename)[0]
                
                messagebox.showinfo("Success", 
                                  f"Document signed successfully!\n\n"
                                  f"Files saved to signed_docs/:\n"
                                  f"• {filename} (original document)\n"
                                  f"• {name_without_ext}.sig (signature)\n"
                                  f"• {name_without_ext}_cert.pem (certificate)")
                
            except Exception as e:
                messagebox.showerror("Signing Error", str(e))
                self.result_label.config(text="Signing failed!", fg="#e74c3c")
            
            finally:
                # Re-enable sign button
                self.sign_button.config(state='normal', text='Sign Document')
        
        # Run signing in separate thread to prevent GUI freezing
        threading.Thread(target=signing_process, daemon=True).start()


def sign_document_gui():
    """Main function to create and run the document signing GUI"""
    root = tk.Tk()
    app = PKIDocumentSignerApp(root)
    root.mainloop()


if __name__ == "__main__":
    sign_document_gui()
