import tkinter as tk
from tkinter import messagebox
import os
from cryptography.hazmat.primitives import serialization, hashes
from cryptography.hazmat.primitives.asymmetric import rsa
from cryptography import x509
from cryptography.x509.oid import NameOID
import datetime
import threading
import time


class PKIRegistrationApp:
    def __init__(self, root):
        self.root = root
        self.setup_ui()
        
    def setup_ui(self):
        """Setup the user interface"""
        self.root.title("PKI Document Signer - Registration")
        self.root.geometry("400x200")
        self.root.resizable(False, False)
        
        # Center the window
        self.center_window()
        
        # Main frame
        main_frame = tk.Frame(self.root, padx=20, pady=20)
        main_frame.pack(expand=True, fill='both')
        
        # Username label
        username_label = tk.Label(main_frame, text="Enter your username:", 
                                font=('Arial', 12))
        username_label.pack(pady=(0, 10))
        
        # Username entry
        self.username_entry = tk.Entry(main_frame, font=('Arial', 11), width=30)
        self.username_entry.pack(pady=(0, 20))
        self.username_entry.bind('<Return>', lambda event: self.register_user())
        
        # Register button
        self.register_button = tk.Button(main_frame, text="Register", 
                                       font=('Arial', 12), 
                                       command=self.register_user,
                                       bg='#4CAF50', fg='white',
                                       padx=20, pady=5)
        self.register_button.pack()
        
        # Focus on username entry
        self.username_entry.focus()
    
    def center_window(self):
        """Center the window on the screen"""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f'{width}x{height}+{x}+{y}')
    
    def create_directories(self):
        """Create necessary directories if they don't exist"""
        os.makedirs('keys', exist_ok=True)
        os.makedirs('certs', exist_ok=True)
    
    def generate_rsa_keypair(self):
        """Generate RSA key pair (2048 bits)"""
        private_key = rsa.generate_private_key(
            public_exponent=65537,
            key_size=2048
        )
        public_key = private_key.public_key()
        return private_key, public_key
    
    def save_private_key(self, private_key, username):
        """Save private key to file"""
        private_pem = private_key.private_bytes(
            encoding=serialization.Encoding.PEM,
            format=serialization.PrivateFormat.PKCS8,
            encryption_algorithm=serialization.NoEncryption()
        )
        
        with open(f'keys/{username}_private.pem', 'wb') as f:
            f.write(private_pem)
    
    def save_public_key(self, public_key, username):
        """Save public key to file"""
        public_pem = public_key.public_bytes(
            encoding=serialization.Encoding.PEM,
            format=serialization.PublicFormat.SubjectPublicKeyInfo
        )
        
        with open(f'keys/{username}_public.pem', 'wb') as f:
            f.write(public_pem)
    
    def generate_self_signed_certificate(self, private_key, public_key, username):
        """Generate self-signed certificate"""
        subject = issuer = x509.Name([
            x509.NameAttribute(NameOID.COUNTRY_NAME, "US"),
            x509.NameAttribute(NameOID.STATE_OR_PROVINCE_NAME, "State"),
            x509.NameAttribute(NameOID.LOCALITY_NAME, "City"),
            x509.NameAttribute(NameOID.ORGANIZATION_NAME, "PKI Document Signer"),
            x509.NameAttribute(NameOID.COMMON_NAME, username),
        ])
        
        cert = x509.CertificateBuilder().subject_name(
            subject
        ).issuer_name(
            issuer
        ).public_key(
            public_key
        ).serial_number(
            x509.random_serial_number()
        ).not_valid_before(
            datetime.datetime.utcnow()
        ).not_valid_after(
            datetime.datetime.utcnow() + datetime.timedelta(days=365)
        ).add_extension(
            x509.SubjectAlternativeName([
                x509.DNSName("localhost"),
            ]),
            critical=False,
        ).sign(private_key, hashes.SHA256())
        
        return cert
    
    def save_certificate(self, certificate, username):
        """Save certificate to file"""
        cert_pem = certificate.public_bytes(serialization.Encoding.PEM)
        
        with open(f'certs/{username}_cert.pem', 'wb') as f:
            f.write(cert_pem)
    
    def show_success_popup(self):
        """Show success popup with fade-in animation"""
        popup = tk.Toplevel(self.root)
        popup.title("Success")
        popup.geometry("300x100")
        popup.resizable(False, False)
        popup.transient(self.root)
        popup.grab_set()
        
        # Center popup
        popup.update_idletasks()
        x = (popup.winfo_screenwidth() // 2) - (popup.winfo_width() // 2)
        y = (popup.winfo_screenheight() // 2) - (popup.winfo_height() // 2)
        popup.geometry(f'+{x}+{y}')
        
        # Start with transparent window
        popup.attributes('-alpha', 0.0)
        
        # Success message
        message_label = tk.Label(popup, text="Registration successful!", 
                               font=('Arial', 12), fg='green')
        message_label.pack(expand=True)
        
        # OK button
        ok_button = tk.Button(popup, text="OK", command=popup.destroy,
                            font=('Arial', 10), padx=20)
        ok_button.pack(pady=(0, 10))
        
        # Fade-in animation
        def fade_in():
            alpha = 0.0
            while alpha < 1.0:
                alpha += 0.05
                popup.attributes('-alpha', alpha)
                time.sleep(0.02)
        
        # Run fade-in in separate thread
        threading.Thread(target=fade_in, daemon=True).start()
    
    def register_user(self):
        """Handle user registration"""
        username = self.username_entry.get().strip()
        
        if not username:
            messagebox.showerror("Error", "Please enter a username!")
            return
        
        if not username.replace('_', '').replace('-', '').isalnum():
            messagebox.showerror("Error", "Username can only contain letters, numbers, hyphens, and underscores!")
            return
        
        try:
            # Disable button during processing
            self.register_button.config(state='disabled', text='Processing...')
            self.root.update()
            
            # Create directories
            self.create_directories()
            
            # Generate RSA key pair
            private_key, public_key = self.generate_rsa_keypair()
            
            # Save keys
            self.save_private_key(private_key, username)
            self.save_public_key(public_key, username)
            
            # Generate and save certificate
            certificate = self.generate_self_signed_certificate(private_key, public_key, username)
            self.save_certificate(certificate, username)
            
            # Show success popup
            self.show_success_popup()
            
            # Clear username field
            self.username_entry.delete(0, tk.END)
            
        except Exception as e:
            messagebox.showerror("Error", f"Registration failed: {str(e)}")
        
        finally:
            # Re-enable button
            self.register_button.config(state='normal', text='Register')


def main():
    """Main function to run the application"""
    root = tk.Tk()
    app = PKIRegistrationApp(root)
    root.mainloop()


if __name__ == "__main__":
    main()
