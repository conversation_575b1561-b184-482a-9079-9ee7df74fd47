#!/usr/bin/env python3
"""
Test GUI-based document verification with existing signed documents
"""

import os
import tkinter as tk
from pki_document_verifier import PKIDocumentVerifierApp


def test_gui_verification_with_existing_files():
    """Test GUI verification with existing signed documents"""
    print("Testing GUI Document Verification")
    print("=" * 40)
    
    # Check if signed documents exist
    if not os.path.exists('signed_docs'):
        print("❌ No signed_docs directory found!")
        print("Please run the document signer first to create signed documents.")
        return False
    
    files = os.listdir('signed_docs')
    doc_files = [f for f in files if not f.endswith('.sig') and not f.endswith('_cert.pem')]
    
    if not doc_files:
        print("❌ No signed documents found!")
        print("Please run the document signer first to create signed documents.")
        return False
    
    # Test with first available document
    doc_name = doc_files[0]
    name_without_ext = os.path.splitext(doc_name)[0]
    
    doc_path = f'signed_docs/{doc_name}'
    sig_path = f'signed_docs/{name_without_ext}.sig'
    cert_path = f'signed_docs/{name_without_ext}_cert.pem'
    
    # Verify all required files exist
    if not all(os.path.exists(path) for path in [doc_path, sig_path, cert_path]):
        print("❌ Incomplete signed document files!")
        return False
    
    print(f"Testing with document: {doc_name}")
    print(f"Document path: {doc_path}")
    print(f"Signature path: {sig_path}")
    print(f"Certificate path: {cert_path}")
    
    try:
        # Create GUI app instance
        root = tk.Tk()
        root.withdraw()  # Hide the window for testing
        app = PKIDocumentVerifierApp(root)
        
        # Set file paths (simulating user selection)
        app.document_path = doc_path
        app.signature_path = sig_path
        app.certificate_path = cert_path
        
        print("\n1. Loading document and computing hash...")
        document_content, document_hash = app.load_document(app.document_path)
        print(f"   ✓ Document loaded: {len(document_content)} bytes")
        print(f"   ✓ SHA-256 hash computed: {len(document_hash)} bytes")
        
        print("\n2. Loading digital signature...")
        signature = app.load_signature(app.signature_path)
        print(f"   ✓ Signature loaded: {len(signature)} bytes")
        
        print("\n3. Loading certificate and extracting public key...")
        certificate, public_key = app.load_certificate(app.certificate_path)
        print(f"   ✓ Certificate loaded successfully")
        print(f"   ✓ Public key extracted: {public_key.key_size} bits")
        
        print("\n4. Verifying signature...")
        verification_result = app.verify_signature(public_key, document_hash, signature)
        
        if verification_result:
            print("   ✅ SIGNATURE VERIFICATION SUCCESSFUL")
            print("   The document is authentic and has not been tampered with.")
        else:
            print("   ❌ SIGNATURE VERIFICATION FAILED")
            print("   The document may have been tampered with.")
        
        print("\n5. Extracting certificate information...")
        cert_info = app.get_certificate_info(certificate)
        print(f"   Signer: {cert_info['common_name']}")
        print(f"   Organization: {cert_info['organization']}")
        print(f"   Country: {cert_info['country']}")
        print(f"   Valid from: {cert_info['not_valid_before']}")
        print(f"   Valid until: {cert_info['not_valid_after']}")
        
        root.destroy()
        
        if verification_result:
            print("\n🎉 GUI verification test completed successfully!")
            return True
        else:
            print("\n❌ GUI verification test failed - signature invalid!")
            return False
            
    except Exception as e:
        print(f"\n❌ GUI verification test failed with error: {str(e)}")
        return False


def test_tampered_document_detection():
    """Test detection of tampered documents"""
    print("\n" + "=" * 40)
    print("Testing Tampered Document Detection")
    print("=" * 40)
    
    if not os.path.exists('signed_docs/sample_document.txt'):
        print("⚠ No sample document found for tampering test")
        return True
    
    try:
        # Create a tampered version of the document
        with open('signed_docs/sample_document.txt', 'rb') as f:
            original_content = f.read()
        
        # Create tampered content
        tampered_content = original_content.replace(b'sample', b'TAMPERED')
        
        # Save tampered document temporarily
        tampered_path = 'signed_docs/tampered_sample.txt'
        with open(tampered_path, 'wb') as f:
            f.write(tampered_content)
        
        # Test verification with tampered document
        root = tk.Tk()
        root.withdraw()
        app = PKIDocumentVerifierApp(root)
        
        # Use tampered document with original signature and certificate
        app.document_path = tampered_path
        app.signature_path = 'signed_docs/sample_document.sig'
        app.certificate_path = 'signed_docs/sample_document_cert.pem'
        
        print("1. Testing with tampered document...")
        document_content, document_hash = app.load_document(app.document_path)
        signature = app.load_signature(app.signature_path)
        certificate, public_key = app.load_certificate(app.certificate_path)
        
        verification_result = app.verify_signature(public_key, document_hash, signature)
        
        if not verification_result:
            print("   ✅ TAMPERING DETECTED SUCCESSFULLY")
            print("   The system correctly identified the tampered document.")
        else:
            print("   ❌ TAMPERING NOT DETECTED")
            print("   The system failed to detect document tampering.")
        
        # Clean up
        os.remove(tampered_path)
        root.destroy()
        
        return not verification_result  # Should return True if tampering was detected
        
    except Exception as e:
        print(f"❌ Tampering detection test failed: {str(e)}")
        return False


def main():
    """Main function"""
    print("PKI Document Verifier - GUI Testing")
    print("=" * 50)
    
    success1 = test_gui_verification_with_existing_files()
    success2 = test_tampered_document_detection()
    
    if success1 and success2:
        print("\n🎉 All GUI verification tests passed!")
        print("\nThe PKI Document Verifier GUI is fully functional:")
        print("  ✓ Document loading and hashing")
        print("  ✓ Signature file loading")
        print("  ✓ Certificate loading and public key extraction")
        print("  ✓ Signature verification with RSA-PSS")
        print("  ✓ Certificate information display")
        print("  ✓ Tampered document detection")
        
        print("\nYou can now:")
        print("  1. Run 'python pki_document_verifier.py' for GUI verification")
        print("  2. Use the demo system to launch all applications")
        print("  3. Verify any signed documents in the signed_docs/ folder")
        
        return 0
    else:
        print("\n❌ Some GUI verification tests failed.")
        return 1


if __name__ == "__main__":
    exit(main())
