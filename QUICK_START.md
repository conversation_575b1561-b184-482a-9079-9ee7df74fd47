# PKI Document Signer - Quick Start Guide

## 🚀 Getting Started

### Prerequisites
- Python 3.6 or higher
- `cryptography` library: `pip install cryptography`

### 1. Launch the Demo System
```bash
python demo_pki_system.py
```

This opens a unified interface where you can:
- Launch all applications
- Monitor system status
- Follow guided workflows

## 📋 Step-by-Step Workflow

### Step 1: Register a User
1. Click "1. Open Registration" in the demo system
2. Enter a username (letters, numbers, hyphens, underscores only)
3. Click "Register"
4. Wait for the success message

**What happens**: Creates RSA key pair (2048-bit) and self-signed certificate

### Step 2: Test Authentication
1. Click "2. Open Login" in the demo system
2. Select your private key from `keys/<username>_private.pem`
3. Select your certificate from `certs/<username>_cert.pem`
4. Click "Login"

**What happens**: Challenge-response authentication using digital signatures

### Step 3: Sign a Document
1. Click "3. Open Document Signer" in the demo system
2. Select a document to sign (try `sample_document.txt`)
3. Select your private key from `keys/<username>_private.pem`
4. Select your certificate from `certs/<username>_cert.pem`
5. Click "Sign Document"

**What happens**: Creates digital signature and saves files to `signed_docs/`

### Step 4: Verify Signed Documents
```bash
# List all signed documents
python verify_signed_document.py --list

# Verify a specific document
python verify_signed_document.py sample_document.txt
```

## 🧪 Testing

Run comprehensive tests to verify everything works:

```bash
# Test individual components
python test_pki_registration.py
python test_pki_login.py
python test_pki_document_signer.py

# Test complete workflow
python test_complete_workflow.py
```

## 📁 File Organization

After using the system, you'll have:

```
project_directory/
├── keys/                    # Private and public keys
│   ├── <username>_private.pem
│   └── <username>_public.pem
├── certs/                   # X.509 certificates
│   └── <username>_cert.pem
└── signed_docs/             # Signed documents
    ├── <document_name>      # Original document
    ├── <document>.sig       # Digital signature
    └── <document>_cert.pem  # Associated certificate
```

## 🔐 Security Features

- **RSA-2048**: Industry-standard key size
- **SHA-256**: Secure hashing algorithm
- **RSA-PSS**: Probabilistic signature scheme
- **X.509 Certificates**: Standard certificate format
- **Challenge-Response**: Prevents replay attacks

## 🎯 Use Cases

### Educational
- Learn PKI concepts
- Understand digital signatures
- Practice cryptographic operations

### Development
- Prototype document signing systems
- Test certificate-based authentication
- Experiment with cryptographic libraries

### Demonstration
- Show PKI workflow to stakeholders
- Demonstrate security concepts
- Validate cryptographic implementations

## 🔧 Troubleshooting

### Common Issues

**"No registered users found"**
- Solution: Run registration first to create keys and certificates

**"Failed to load private key"**
- Solution: Ensure the selected file is a valid PEM private key

**"Signature verification failed"**
- Solution: Check that the certificate matches the private key used for signing

**"Permission denied" errors**
- Solution: Ensure you have write permissions in the project directory

### Getting Help

1. Check the error messages - they're designed to be helpful
2. Run the test suites to verify system functionality
3. Review the README.md for detailed documentation
4. Check SYSTEM_OVERVIEW.md for technical details

## 🎉 Success Indicators

You'll know everything is working when:
- ✅ Registration creates keys and certificates
- ✅ Login authentication succeeds
- ✅ Documents are signed successfully
- ✅ Signature verification passes
- ✅ All tests pass

## 📚 Next Steps

Once you're comfortable with the basic workflow:

1. **Try Different Document Types**: Sign PDFs, Word docs, images, etc.
2. **Multiple Users**: Register several users and test cross-verification
3. **Custom Documents**: Create your own documents to sign
4. **Integration**: Use the components in your own applications

## 🛡️ Security Notes

This is a **demonstration system** for learning purposes:
- Private keys are stored unencrypted
- Self-signed certificates (no CA validation)
- Suitable for development and education
- Not recommended for production use without security enhancements

For production use, consider:
- Password-protected private keys
- Certificate Authority (CA) integration
- Hardware Security Modules (HSM)
- Key rotation and expiration handling

---

**Happy signing! 🔏**
