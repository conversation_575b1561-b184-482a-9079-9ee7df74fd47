#!/usr/bin/env python3
"""
Test script to verify registration functionality
"""

import os
import sys
import sqlite3
from datetime import datetime, timezone, timedelta

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from pki_system_main import DatabaseManager


def test_registration():
    """Test the registration process"""
    print("🧪 Testing PKI Registration Process")
    print("=" * 40)
    
    try:
        # Initialize database
        db = DatabaseManager()
        print("✅ Database initialized successfully")
        
        # Test database connection
        with sqlite3.connect("pki_system.db") as conn:
            cursor = conn.cursor()
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = cursor.fetchall()
            print(f"✅ Found {len(tables)} tables: {[t[0] for t in tables]}")
        
        # Test datetime functionality
        now = datetime.now(timezone.utc)
        future = now + timedelta(days=365)
        print(f"✅ Datetime functions working: {now.strftime('%Y-%m-%d %H:%M:%S')} UTC")
        
        # Test user statistics
        stats = db.get_user_statistics()
        print(f"✅ Current statistics: {stats}")
        
        print("\n🎉 All tests passed! Registration should work now.")
        print("\nTo test registration:")
        print("1. Run: python pki_system_main.py")
        print("2. Enter a username (e.g., 'testuser')")
        print("3. Click 'Register User'")
        print("4. Check the database with: python database_viewer.py")
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        return False


def main():
    """Main test function"""
    success = test_registration()
    
    if success:
        print("\n✅ System is ready for registration!")
    else:
        print("\n❌ System has issues that need to be fixed.")
    
    input("\nPress Enter to exit...")


if __name__ == "__main__":
    main()
