#!/usr/bin/env python3
"""
Test script for PKI Login functionality
"""

import os
import tempfile
import shutil
from cryptography.hazmat.primitives import serialization, hashes
from cryptography.hazmat.primitives.asymmetric import rsa, padding
from cryptography import x509
from cryptography.x509.oid import NameOID
import datetime
import tkinter as tk
from pki_login import PKILoginApp


def create_test_keys_and_cert(username="testuser"):
    """Create test keys and certificate for testing"""
    # Generate RSA key pair
    private_key = rsa.generate_private_key(
        public_exponent=65537,
        key_size=2048
    )
    public_key = private_key.public_key()
    
    # Create directories
    os.makedirs('test_keys', exist_ok=True)
    os.makedirs('test_certs', exist_ok=True)
    
    # Save private key
    private_pem = private_key.private_bytes(
        encoding=serialization.Encoding.PEM,
        format=serialization.PrivateFormat.PKCS8,
        encryption_algorithm=serialization.NoEncryption()
    )
    
    private_key_path = f'test_keys/{username}_private.pem'
    with open(private_key_path, 'wb') as f:
        f.write(private_pem)
    
    # Generate self-signed certificate
    subject = issuer = x509.Name([
        x509.NameAttribute(NameOID.COUNTRY_NAME, "US"),
        x509.NameAttribute(NameOID.STATE_OR_PROVINCE_NAME, "State"),
        x509.NameAttribute(NameOID.LOCALITY_NAME, "City"),
        x509.NameAttribute(NameOID.ORGANIZATION_NAME, "PKI Document Signer"),
        x509.NameAttribute(NameOID.COMMON_NAME, username),
    ])
    
    cert = x509.CertificateBuilder().subject_name(
        subject
    ).issuer_name(
        issuer
    ).public_key(
        public_key
    ).serial_number(
        x509.random_serial_number()
    ).not_valid_before(
        datetime.datetime.utcnow()
    ).not_valid_after(
        datetime.datetime.utcnow() + datetime.timedelta(days=365)
    ).sign(private_key, hashes.SHA256())
    
    # Save certificate
    cert_pem = cert.public_bytes(serialization.Encoding.PEM)
    cert_path = f'test_certs/{username}_cert.pem'
    with open(cert_path, 'wb') as f:
        f.write(cert_pem)
    
    return private_key_path, cert_path


def test_authentication_logic():
    """Test the core authentication logic"""
    print("Testing PKI authentication logic...")
    
    # Create test files
    private_key_path, cert_path = create_test_keys_and_cert("testauth")
    
    try:
        # Create app instance for testing
        root = tk.Tk()
        root.withdraw()  # Hide the window
        app = PKILoginApp(root)
        
        # Test loading certificate and extracting public key
        certificate, public_key = app.load_certificate(cert_path)
        assert certificate is not None, "Certificate should be loaded"
        assert public_key is not None, "Public key should be extracted"
        
        # Test loading private key
        private_key = app.load_private_key(private_key_path)
        assert private_key is not None, "Private key should be loaded"
        assert private_key.key_size == 2048, "Private key should be 2048 bits"
        
        # Test challenge generation
        challenge = app.generate_challenge()
        assert len(challenge) == 32, "Challenge should be 32 characters"
        assert challenge.isalnum(), "Challenge should be alphanumeric"
        
        # Test signing
        signature = app.sign_challenge(private_key, challenge)
        assert signature is not None, "Signature should be generated"
        
        # Test verification with correct key pair
        verification_result = app.verify_signature(public_key, challenge, signature)
        assert verification_result == True, "Verification should succeed with correct key pair"
        
        # Test verification with wrong challenge
        wrong_challenge = app.generate_challenge()
        verification_result_wrong = app.verify_signature(public_key, wrong_challenge, signature)
        assert verification_result_wrong == False, "Verification should fail with wrong challenge"
        
        print("✓ Authentication logic test passed")
        root.destroy()
        
    finally:
        # Clean up test files
        cleanup_test_files("testauth")


def test_file_operations():
    """Test file loading operations"""
    print("Testing file operations...")
    
    # Create test files
    private_key_path, cert_path = create_test_keys_and_cert("testfile")
    
    try:
        # Create app instance
        root = tk.Tk()
        root.withdraw()
        app = PKILoginApp(root)
        
        # Test valid file loading
        try:
            private_key = app.load_private_key(private_key_path)
            certificate, public_key = app.load_certificate(cert_path)
            print("✓ Valid file loading test passed")
        except Exception as e:
            raise AssertionError(f"Valid file loading should not fail: {e}")
        
        # Test invalid file loading
        try:
            app.load_private_key("nonexistent_file.pem")
            raise AssertionError("Loading nonexistent file should fail")
        except Exception:
            print("✓ Invalid file handling test passed")
        
        root.destroy()
        
    finally:
        # Clean up test files
        cleanup_test_files("testfile")


def test_cross_compatibility():
    """Test compatibility with registration app output"""
    print("Testing cross-compatibility with registration app...")
    
    # Check if registration app created any files
    if os.path.exists('keys') and os.path.exists('certs'):
        key_files = [f for f in os.listdir('keys') if f.endswith('_private.pem')]
        cert_files = [f for f in os.listdir('certs') if f.endswith('_cert.pem')]
        
        if key_files and cert_files:
            # Test with first available user
            username = key_files[0].replace('_private.pem', '')
            private_key_path = f'keys/{username}_private.pem'
            cert_path = f'certs/{username}_cert.pem'
            
            if os.path.exists(cert_path):
                root = tk.Tk()
                root.withdraw()
                app = PKILoginApp(root)
                
                try:
                    # Test authentication with registration app files
                    certificate, public_key = app.load_certificate(cert_path)
                    private_key = app.load_private_key(private_key_path)
                    
                    challenge = app.generate_challenge()
                    signature = app.sign_challenge(private_key, challenge)
                    result = app.verify_signature(public_key, challenge, signature)
                    
                    assert result == True, "Authentication should work with registration app files"
                    print("✓ Cross-compatibility test passed")
                    
                except Exception as e:
                    print(f"✗ Cross-compatibility test failed: {e}")
                
                root.destroy()
            else:
                print("⚠ No registration app files found for cross-compatibility test")
        else:
            print("⚠ No registration app files found for cross-compatibility test")
    else:
        print("⚠ No registration app directories found for cross-compatibility test")


def cleanup_test_files(username):
    """Clean up test files"""
    files_to_remove = [
        f'test_keys/{username}_private.pem',
        f'test_certs/{username}_cert.pem'
    ]
    
    for file_path in files_to_remove:
        if os.path.exists(file_path):
            os.remove(file_path)
    
    # Remove test directories if empty
    for dir_path in ['test_keys', 'test_certs']:
        if os.path.exists(dir_path) and not os.listdir(dir_path):
            os.rmdir(dir_path)


def main():
    """Run all tests"""
    print("Running PKI Login tests...\n")
    
    try:
        test_authentication_logic()
        test_file_operations()
        test_cross_compatibility()
        print("\n✓ All tests passed successfully!")
        
    except Exception as e:
        print(f"\n✗ Test failed: {e}")
        return 1
    
    return 0


if __name__ == "__main__":
    exit(main())
