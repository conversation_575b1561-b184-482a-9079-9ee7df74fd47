#!/usr/bin/env python3
"""
Test script for PKI Document Signer functionality
"""

import os
import tempfile
import shutil
import hashlib
from cryptography.hazmat.primitives import serialization, hashes
from cryptography.hazmat.primitives.asymmetric import rsa, padding
from cryptography import x509
from cryptography.x509.oid import NameOID
import datetime
import tkinter as tk
from pki_document_signer import PKIDocumentSignerApp


def create_test_files():
    """Create test document, keys, and certificate"""
    # Create test document
    test_doc_content = b"This is a test document for PKI signing.\nIt contains multiple lines.\nAnd some special characters: @#$%^&*()"
    
    os.makedirs('test_files', exist_ok=True)
    
    # Save test document
    test_doc_path = 'test_files/test_document.txt'
    with open(test_doc_path, 'wb') as f:
        f.write(test_doc_content)
    
    # Generate RSA key pair
    private_key = rsa.generate_private_key(
        public_exponent=65537,
        key_size=2048
    )
    public_key = private_key.public_key()
    
    # Save private key
    private_pem = private_key.private_bytes(
        encoding=serialization.Encoding.PEM,
        format=serialization.PrivateFormat.PKCS8,
        encryption_algorithm=serialization.NoEncryption()
    )
    
    private_key_path = 'test_files/test_private.pem'
    with open(private_key_path, 'wb') as f:
        f.write(private_pem)
    
    # Generate self-signed certificate
    subject = issuer = x509.Name([
        x509.NameAttribute(NameOID.COUNTRY_NAME, "US"),
        x509.NameAttribute(NameOID.STATE_OR_PROVINCE_NAME, "State"),
        x509.NameAttribute(NameOID.LOCALITY_NAME, "City"),
        x509.NameAttribute(NameOID.ORGANIZATION_NAME, "PKI Document Signer"),
        x509.NameAttribute(NameOID.COMMON_NAME, "testuser"),
    ])
    
    cert = x509.CertificateBuilder().subject_name(
        subject
    ).issuer_name(
        issuer
    ).public_key(
        public_key
    ).serial_number(
        x509.random_serial_number()
    ).not_valid_before(
        datetime.datetime.now(datetime.timezone.utc)
    ).not_valid_after(
        datetime.datetime.now(datetime.timezone.utc) + datetime.timedelta(days=365)
    ).sign(private_key, hashes.SHA256())
    
    # Save certificate
    cert_pem = cert.public_bytes(serialization.Encoding.PEM)
    cert_path = 'test_files/test_cert.pem'
    with open(cert_path, 'wb') as f:
        f.write(cert_pem)
    
    return test_doc_path, private_key_path, cert_path, test_doc_content


def test_document_signing_logic():
    """Test the core document signing logic"""
    print("Testing document signing logic...")
    
    # Create test files
    doc_path, private_key_path, cert_path, doc_content = create_test_files()
    
    try:
        # Create app instance for testing
        root = tk.Tk()
        root.withdraw()  # Hide the window
        app = PKIDocumentSignerApp(root)
        
        # Test document reading
        read_content = app.read_document_content(doc_path)
        assert read_content == doc_content, "Document content should match"
        
        # Test hashing
        expected_hash = hashlib.sha256(doc_content).digest()
        actual_hash = app.hash_content(doc_content)
        assert actual_hash == expected_hash, "Hash should match expected value"
        
        # Test private key loading
        private_key = app.load_private_key(private_key_path)
        assert private_key is not None, "Private key should be loaded"
        assert private_key.key_size == 2048, "Private key should be 2048 bits"
        
        # Test signing
        signature = app.sign_hash(private_key, actual_hash)
        assert signature is not None, "Signature should be generated"
        assert len(signature) > 0, "Signature should not be empty"
        
        # Test signature verification (using public key from private key)
        public_key = private_key.public_key()
        try:
            public_key.verify(
                signature,
                actual_hash,
                padding.PSS(
                    mgf=padding.MGF1(hashes.SHA256()),
                    salt_length=padding.PSS.MAX_LENGTH
                ),
                hashes.SHA256()
            )
            print("✓ Signature verification successful")
        except Exception:
            raise AssertionError("Signature verification should succeed")
        
        print("✓ Document signing logic test passed")
        root.destroy()
        
    finally:
        # Clean up test files
        cleanup_test_files()


def test_file_operations():
    """Test file saving operations"""
    print("Testing file operations...")
    
    # Create test files
    doc_path, private_key_path, cert_path, doc_content = create_test_files()
    
    try:
        # Create app instance
        root = tk.Tk()
        root.withdraw()
        app = PKIDocumentSignerApp(root)
        
        # Create a test signature
        private_key = app.load_private_key(private_key_path)
        doc_hash = app.hash_content(doc_content)
        signature = app.sign_hash(private_key, doc_hash)
        
        # Test saving signed files
        saved_paths = app.save_signed_files(doc_path, signature, cert_path)
        
        # Verify files were created
        assert len(saved_paths) == 3, "Should return 3 file paths"
        
        signed_doc_path, signature_path, cert_dest_path = saved_paths
        
        assert os.path.exists(signed_doc_path), "Signed document should exist"
        assert os.path.exists(signature_path), "Signature file should exist"
        assert os.path.exists(cert_dest_path), "Certificate file should exist"
        
        # Verify signed_docs folder was created
        assert os.path.exists('signed_docs'), "signed_docs folder should exist"
        
        # Verify file contents
        with open(signed_doc_path, 'rb') as f:
            saved_doc_content = f.read()
        assert saved_doc_content == doc_content, "Saved document content should match original"
        
        with open(signature_path, 'rb') as f:
            saved_signature = f.read()
        assert saved_signature == signature, "Saved signature should match original"
        
        print("✓ File operations test passed")
        root.destroy()
        
    finally:
        # Clean up test files
        cleanup_test_files()


def test_end_to_end_workflow():
    """Test complete end-to-end workflow"""
    print("Testing end-to-end workflow...")
    
    # Create test files
    doc_path, private_key_path, cert_path, doc_content = create_test_files()
    
    try:
        # Create app instance
        root = tk.Tk()
        root.withdraw()
        app = PKIDocumentSignerApp(root)
        
        # Set file paths (simulating user selection)
        app.document_path = doc_path
        app.private_key_path = private_key_path
        app.certificate_path = cert_path
        
        # Perform complete signing workflow
        document_content = app.read_document_content(app.document_path)
        document_hash = app.hash_content(document_content)
        private_key = app.load_private_key(app.private_key_path)
        signature = app.sign_hash(private_key, document_hash)
        saved_paths = app.save_signed_files(app.document_path, signature, app.certificate_path)
        
        # Verify complete workflow
        assert os.path.exists('signed_docs'), "signed_docs folder should be created"
        
        filename = os.path.basename(doc_path)
        name_without_ext = os.path.splitext(filename)[0]
        
        expected_files = [
            f'signed_docs/{filename}',
            f'signed_docs/{name_without_ext}.sig',
            f'signed_docs/{name_without_ext}_cert.pem'
        ]
        
        for expected_file in expected_files:
            assert os.path.exists(expected_file), f"File {expected_file} should exist"
        
        print("✓ End-to-end workflow test passed")
        root.destroy()
        
    finally:
        # Clean up test files
        cleanup_test_files()


def test_cross_compatibility():
    """Test compatibility with existing PKI system files"""
    print("Testing cross-compatibility with existing PKI system...")
    
    # Check if existing PKI files are available
    if os.path.exists('keys') and os.path.exists('certs'):
        key_files = [f for f in os.listdir('keys') if f.endswith('_private.pem')]
        cert_files = [f for f in os.listdir('certs') if f.endswith('_cert.pem')]
        
        if key_files and cert_files:
            # Test with first available user
            username = key_files[0].replace('_private.pem', '')
            private_key_path = f'keys/{username}_private.pem'
            cert_path = f'certs/{username}_cert.pem'
            
            if os.path.exists(cert_path):
                # Create test document
                test_doc_path = 'test_cross_compat.txt'
                test_content = b"Cross-compatibility test document"
                
                with open(test_doc_path, 'wb') as f:
                    f.write(test_content)
                
                try:
                    root = tk.Tk()
                    root.withdraw()
                    app = PKIDocumentSignerApp(root)
                    
                    # Test signing with existing PKI files
                    document_content = app.read_document_content(test_doc_path)
                    document_hash = app.hash_content(document_content)
                    private_key = app.load_private_key(private_key_path)
                    signature = app.sign_hash(private_key, document_hash)
                    
                    # Verify signature can be created
                    assert signature is not None, "Should be able to sign with existing PKI files"
                    
                    print("✓ Cross-compatibility test passed")
                    root.destroy()
                    
                except Exception as e:
                    print(f"✗ Cross-compatibility test failed: {e}")
                
                finally:
                    # Clean up test document
                    if os.path.exists(test_doc_path):
                        os.remove(test_doc_path)
            else:
                print("⚠ No matching certificate found for cross-compatibility test")
        else:
            print("⚠ No existing PKI files found for cross-compatibility test")
    else:
        print("⚠ No existing PKI directories found for cross-compatibility test")


def cleanup_test_files():
    """Clean up all test files and directories"""
    # Remove test files directory
    if os.path.exists('test_files'):
        shutil.rmtree('test_files')
    
    # Remove signed_docs directory
    if os.path.exists('signed_docs'):
        shutil.rmtree('signed_docs')


def main():
    """Run all tests"""
    print("Running PKI Document Signer tests...\n")
    
    try:
        test_document_signing_logic()
        test_file_operations()
        test_end_to_end_workflow()
        test_cross_compatibility()
        print("\n✓ All tests passed successfully!")
        
    except Exception as e:
        print(f"\n✗ Test failed: {e}")
        return 1
    
    return 0


if __name__ == "__main__":
    exit(main())
