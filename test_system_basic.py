#!/usr/bin/env python3
"""
Basic system test without Unicode characters for Windows compatibility
"""

import os
import tkinter as tk
from pki_registration import PKIRegistrationApp
from pki_login import PKILoginApp
from pki_document_signer import PKIDocumentSignerApp
from pki_document_verifier import PKIDocumentVerifierApp
from legal_use_case import LegalUseCaseWindow


def test_basic_functionality():
    """Test basic functionality of all components"""
    print("Testing Basic System Functionality")
    print("=" * 40)
    
    success_count = 0
    total_tests = 0
    
    # Test 1: Registration App
    total_tests += 1
    try:
        root = tk.Tk()
        root.withdraw()
        app = PKIRegistrationApp(root)
        
        # Test key generation
        private_key, public_key = app.generate_rsa_keypair()
        assert private_key.key_size == 2048
        assert public_key.key_size == 2048
        
        print("[PASS] Registration App - Key generation works")
        success_count += 1
        root.destroy()
    except Exception as e:
        print(f"[FAIL] Registration App - {str(e)}")
    
    # Test 2: Login App
    total_tests += 1
    try:
        root = tk.Tk()
        root.withdraw()
        app = PKILoginApp(root)
        
        # Test challenge generation
        challenge = app.generate_challenge()
        assert len(challenge) == 32
        assert challenge.isalnum()
        
        print("[PASS] Login App - Challenge generation works")
        success_count += 1
        root.destroy()
    except Exception as e:
        print(f"[FAIL] Login App - {str(e)}")
    
    # Test 3: Document Signer App
    total_tests += 1
    try:
        root = tk.Tk()
        root.withdraw()
        app = PKIDocumentSignerApp(root)
        
        # Test hashing
        test_content = b"Test document content"
        hash_result = app.hash_content(test_content)
        assert len(hash_result) == 32  # SHA-256 produces 32-byte hash
        
        print("[PASS] Document Signer App - Content hashing works")
        success_count += 1
        root.destroy()
    except Exception as e:
        print(f"[FAIL] Document Signer App - {str(e)}")
    
    # Test 4: Document Verifier App
    total_tests += 1
    try:
        root = tk.Tk()
        root.withdraw()
        app = PKIDocumentVerifierApp(root)

        # Test that the app initializes correctly
        assert hasattr(app, 'document_path')
        assert hasattr(app, 'signature_path')
        assert hasattr(app, 'certificate_path')

        print("[PASS] Document Verifier App - Initialization works")
        success_count += 1
        root.destroy()
    except Exception as e:
        print(f"[FAIL] Document Verifier App - {str(e)}")
    
    # Test 5: Legal Use Case Window
    total_tests += 1
    try:
        root = tk.Tk()
        root.withdraw()
        app = LegalUseCaseWindow(root)
        
        # Test content exists
        content = app.text_area.get('1.0', 'end')
        assert len(content.strip()) > 1000  # Should have substantial content
        assert "Digital Contract Signing System" in content
        
        print("[PASS] Legal Use Case Window - Content loaded correctly")
        success_count += 1
        root.destroy()
    except Exception as e:
        print(f"[FAIL] Legal Use Case Window - {str(e)}")
    
    return success_count, total_tests


def test_file_structure():
    """Test that all required files exist"""
    print("\nTesting File Structure")
    print("=" * 25)
    
    required_files = [
        'pki_registration.py',
        'pki_login.py',
        'pki_document_signer.py',
        'pki_document_verifier.py',
        'legal_use_case.py',
        'demo_pki_system.py',
        'verify_signed_document.py',
        'README.md',
        'QUICK_START.md',
        'sample_document.txt'
    ]
    
    missing_files = []
    for file in required_files:
        if os.path.exists(file):
            print(f"[FOUND] {file}")
        else:
            print(f"[MISSING] {file}")
            missing_files.append(file)
    
    if missing_files:
        print(f"\n[FAIL] {len(missing_files)} files missing")
        return False
    else:
        print(f"\n[PASS] All {len(required_files)} required files present")
        return True


def test_user_data():
    """Test if user data exists"""
    print("\nTesting User Data")
    print("=" * 20)
    
    if os.path.exists('keys') and os.path.exists('certs'):
        key_files = [f for f in os.listdir('keys') if f.endswith('_private.pem')]
        cert_files = [f for f in os.listdir('certs') if f.endswith('_cert.pem')]
        
        if key_files and cert_files:
            print(f"[PASS] User data found: {len(key_files)} users registered")
            return True
        else:
            print("[INFO] No user data found - run registration first")
            return True  # Not a failure, just informational
    else:
        print("[INFO] No user directories found - run registration first")
        return True  # Not a failure, just informational


def test_signed_documents():
    """Test if signed documents exist"""
    print("\nTesting Signed Documents")
    print("=" * 28)
    
    if os.path.exists('signed_docs'):
        files = os.listdir('signed_docs')
        doc_files = [f for f in files if not f.endswith('.sig') and not f.endswith('_cert.pem')]
        
        if doc_files:
            print(f"[PASS] Signed documents found: {len(doc_files)} documents")
            return True
        else:
            print("[INFO] No signed documents found - run document signer first")
            return True  # Not a failure, just informational
    else:
        print("[INFO] No signed_docs directory found - run document signer first")
        return True  # Not a failure, just informational


def main():
    """Run all basic tests"""
    print("PKI Document Signer System - Basic Testing")
    print("=" * 50)
    
    # Run all tests
    success_count, total_tests = test_basic_functionality()
    file_structure_ok = test_file_structure()
    test_user_data()  # Informational only
    test_signed_documents()  # Informational only
    
    # Summary
    print("\n" + "=" * 50)
    print("TEST SUMMARY")
    print("=" * 50)
    
    print(f"Basic Functionality: {success_count}/{total_tests} tests passed")
    print(f"File Structure: {'PASS' if file_structure_ok else 'FAIL'}")
    
    overall_success = (success_count == total_tests) and file_structure_ok
    
    if overall_success:
        print("\n[SUCCESS] Basic system tests passed!")
        print("\nThe PKI Document Signer System is functional:")
        print("  * All core applications load correctly")
        print("  * Key generation and hashing work")
        print("  * File structure is complete")
        print("  * Legal use case documentation is available")
        
        print("\nTo use the system:")
        print("  1. Run 'python demo_pki_system.py' for guided usage")
        print("  2. Check README.md for detailed documentation")
        print("  3. Use individual applications as needed")
        
        return 0
    else:
        print(f"\n[FAILURE] Some basic tests failed.")
        print("Please check the error messages above.")
        return 1


if __name__ == "__main__":
    exit(main())
