#!/usr/bin/env python3
"""
Test script for PKI Document Verifier functionality
"""

import os
import tempfile
import shutil
import hashlib
from cryptography.hazmat.primitives import serialization, hashes
from cryptography.hazmat.primitives.asymmetric import rsa, padding
from cryptography import x509
from cryptography.x509.oid import NameOID
import datetime
import tkinter as tk
from pki_document_verifier import PKIDocumentVerifierApp


def create_test_files():
    """Create test document, signature, and certificate files"""
    # Create test document
    test_doc_content = b"This is a test document for PKI signature verification.\nIt contains multiple lines and special characters: @#$%^&*()"
    
    os.makedirs('test_verify_files', exist_ok=True)
    
    # Save test document
    test_doc_path = 'test_verify_files/test_document.txt'
    with open(test_doc_path, 'wb') as f:
        f.write(test_doc_content)
    
    # Generate RSA key pair
    private_key = rsa.generate_private_key(
        public_exponent=65537,
        key_size=2048
    )
    public_key = private_key.public_key()
    
    # Generate self-signed certificate
    subject = issuer = x509.Name([
        x509.NameAttribute(NameOID.COUNTRY_NAME, "US"),
        x509.NameAttribute(NameOID.STATE_OR_PROVINCE_NAME, "State"),
        x509.NameAttribute(NameOID.LOCALITY_NAME, "City"),
        x509.NameAttribute(NameOID.ORGANIZATION_NAME, "PKI Document Signer"),
        x509.NameAttribute(NameOID.COMMON_NAME, "testverifier"),
    ])
    
    cert = x509.CertificateBuilder().subject_name(
        subject
    ).issuer_name(
        issuer
    ).public_key(
        public_key
    ).serial_number(
        x509.random_serial_number()
    ).not_valid_before(
        datetime.datetime.now(datetime.timezone.utc)
    ).not_valid_after(
        datetime.datetime.now(datetime.timezone.utc) + datetime.timedelta(days=365)
    ).sign(private_key, hashes.SHA256())
    
    # Save certificate
    cert_pem = cert.public_bytes(serialization.Encoding.PEM)
    cert_path = 'test_verify_files/test_cert.pem'
    with open(cert_path, 'wb') as f:
        f.write(cert_pem)
    
    # Create signature
    document_hash = hashlib.sha256(test_doc_content).digest()
    signature = private_key.sign(
        document_hash,
        padding.PSS(
            mgf=padding.MGF1(hashes.SHA256()),
            salt_length=padding.PSS.MAX_LENGTH
        ),
        hashes.SHA256()
    )
    
    # Save signature
    sig_path = 'test_verify_files/test_document.sig'
    with open(sig_path, 'wb') as f:
        f.write(signature)
    
    return test_doc_path, sig_path, cert_path, test_doc_content


def test_verification_logic():
    """Test the core verification logic"""
    print("Testing document verification logic...")
    
    # Create test files
    doc_path, sig_path, cert_path, doc_content = create_test_files()
    
    try:
        # Create app instance for testing
        root = tk.Tk()
        root.withdraw()  # Hide the window
        app = PKIDocumentVerifierApp(root)
        
        # Test document loading and hashing
        loaded_content, document_hash = app.load_document(doc_path)
        assert loaded_content == doc_content, "Document content should match"
        
        expected_hash = hashlib.sha256(doc_content).digest()
        assert document_hash == expected_hash, "Document hash should match expected value"
        
        # Test signature loading
        signature = app.load_signature(sig_path)
        assert signature is not None, "Signature should be loaded"
        assert len(signature) > 0, "Signature should not be empty"
        
        # Test certificate loading
        certificate, public_key = app.load_certificate(cert_path)
        assert certificate is not None, "Certificate should be loaded"
        assert public_key is not None, "Public key should be extracted"
        
        # Test signature verification with correct files
        verification_result = app.verify_signature(public_key, document_hash, signature)
        assert verification_result == True, "Verification should succeed with correct files"
        
        # Test signature verification with wrong hash
        wrong_hash = hashlib.sha256(b"wrong content").digest()
        verification_result_wrong = app.verify_signature(public_key, wrong_hash, signature)
        assert verification_result_wrong == False, "Verification should fail with wrong hash"
        
        # Test certificate info extraction
        cert_info = app.get_certificate_info(certificate)
        assert cert_info['common_name'] == "testverifier", "Common name should match"
        assert cert_info['organization'] == "PKI Document Signer", "Organization should match"
        
        print("✓ Document verification logic test passed")
        root.destroy()
        
    finally:
        # Clean up test files
        cleanup_test_files()


def test_file_operations():
    """Test file loading operations"""
    print("Testing file operations...")
    
    # Create test files
    doc_path, sig_path, cert_path, doc_content = create_test_files()
    
    try:
        # Create app instance
        root = tk.Tk()
        root.withdraw()
        app = PKIDocumentVerifierApp(root)
        
        # Test valid file loading
        try:
            content, doc_hash = app.load_document(doc_path)
            signature = app.load_signature(sig_path)
            certificate, public_key = app.load_certificate(cert_path)
            print("✓ Valid file loading test passed")
        except Exception as e:
            raise AssertionError(f"Valid file loading should not fail: {e}")
        
        # Test invalid file loading
        try:
            app.load_document("nonexistent_file.txt")
            raise AssertionError("Loading nonexistent document should fail")
        except Exception:
            pass
        
        try:
            app.load_signature("nonexistent_file.sig")
            raise AssertionError("Loading nonexistent signature should fail")
        except Exception:
            pass
        
        try:
            app.load_certificate("nonexistent_file.pem")
            raise AssertionError("Loading nonexistent certificate should fail")
        except Exception:
            pass
        
        print("✓ Invalid file handling test passed")
        root.destroy()
        
    finally:
        # Clean up test files
        cleanup_test_files()


def test_tampered_document():
    """Test verification with tampered document"""
    print("Testing tampered document detection...")
    
    # Create test files
    doc_path, sig_path, cert_path, doc_content = create_test_files()
    
    try:
        # Create app instance
        root = tk.Tk()
        root.withdraw()
        app = PKIDocumentVerifierApp(root)
        
        # Load original signature and certificate
        signature = app.load_signature(sig_path)
        certificate, public_key = app.load_certificate(cert_path)
        
        # Create tampered document
        tampered_content = b"This is a TAMPERED document for PKI signature verification."
        tampered_doc_path = 'test_verify_files/tampered_document.txt'
        with open(tampered_doc_path, 'wb') as f:
            f.write(tampered_content)
        
        # Test verification with tampered document
        tampered_content_loaded, tampered_hash = app.load_document(tampered_doc_path)
        verification_result = app.verify_signature(public_key, tampered_hash, signature)
        
        assert verification_result == False, "Verification should fail with tampered document"
        print("✓ Tampered document detection test passed")
        
        root.destroy()
        
    finally:
        # Clean up test files
        cleanup_test_files()


def test_cross_compatibility():
    """Test compatibility with existing signed documents"""
    print("Testing cross-compatibility with existing signed documents...")
    
    # Check if signed_docs directory exists with files
    if os.path.exists('signed_docs'):
        files = os.listdir('signed_docs')
        doc_files = [f for f in files if not f.endswith('.sig') and not f.endswith('_cert.pem')]
        
        if doc_files:
            # Test with first available signed document
            doc_name = doc_files[0]
            name_without_ext = os.path.splitext(doc_name)[0]
            
            doc_path = f'signed_docs/{doc_name}'
            sig_path = f'signed_docs/{name_without_ext}.sig'
            cert_path = f'signed_docs/{name_without_ext}_cert.pem'
            
            if os.path.exists(sig_path) and os.path.exists(cert_path):
                try:
                    root = tk.Tk()
                    root.withdraw()
                    app = PKIDocumentVerifierApp(root)
                    
                    # Test verification with existing signed document
                    content, doc_hash = app.load_document(doc_path)
                    signature = app.load_signature(sig_path)
                    certificate, public_key = app.load_certificate(cert_path)
                    
                    verification_result = app.verify_signature(public_key, doc_hash, signature)
                    
                    assert verification_result == True, "Verification should succeed with existing signed documents"
                    print("✓ Cross-compatibility test passed")
                    
                    root.destroy()
                    
                except Exception as e:
                    print(f"✗ Cross-compatibility test failed: {e}")
            else:
                print("⚠ Incomplete signed document files found")
        else:
            print("⚠ No signed documents found for cross-compatibility test")
    else:
        print("⚠ No signed_docs directory found for cross-compatibility test")


def cleanup_test_files():
    """Clean up test files"""
    if os.path.exists('test_verify_files'):
        shutil.rmtree('test_verify_files')


def main():
    """Run all tests"""
    print("Running PKI Document Verifier tests...\n")
    
    try:
        test_verification_logic()
        test_file_operations()
        test_tampered_document()
        test_cross_compatibility()
        print("\n✓ All tests passed successfully!")
        
    except Exception as e:
        print(f"\n✗ Test failed: {e}")
        return 1
    
    return 0


if __name__ == "__main__":
    exit(main())
