#!/usr/bin/env python3
"""
Debug version of the dashboard to test button functionality
"""

import tkinter as tk
from tkinter import messagebox
import subprocess
import sys


def test_registration():
    """Test registration button"""
    print("Registration button clicked!")
    try:
        subprocess.Popen([sys.executable, 'pki_registration.py'])
        messagebox.showinfo("Success", "Registration window should have opened!")
    except Exception as e:
        messagebox.showerror("Error", f"Failed to open registration: {str(e)}")


def test_login():
    """Test login button"""
    print("Login button clicked!")
    try:
        subprocess.Popen([sys.executable, 'pki_login.py'])
        messagebox.showinfo("Success", "Login window should have opened!")
    except Exception as e:
        messagebox.showerror("Error", f"Failed to open login: {str(e)}")


def test_signer():
    """Test document signer button"""
    print("Document signer button clicked!")
    try:
        subprocess.Popen([sys.executable, 'pki_document_signer.py'])
        messagebox.showinfo("Success", "Document signer window should have opened!")
    except Exception as e:
        messagebox.showerror("Error", f"Failed to open document signer: {str(e)}")


def test_verifier():
    """Test document verifier button"""
    print("Document verifier button clicked!")
    try:
        subprocess.Popen([sys.executable, 'pki_document_verifier.py'])
        messagebox.showinfo("Success", "Document verifier window should have opened!")
    except Exception as e:
        messagebox.showerror("Error", f"Failed to open document verifier: {str(e)}")


def test_usecase():
    """Test use case button"""
    print("Use case button clicked!")
    try:
        subprocess.Popen([sys.executable, 'legal_use_case.py'])
        messagebox.showinfo("Success", "Legal use case window should have opened!")
    except Exception as e:
        messagebox.showerror("Error", f"Failed to open legal use case: {str(e)}")


def create_debug_dashboard():
    """Create a simple debug dashboard"""
    root = tk.Tk()
    root.title("Debug PKI Dashboard - Button Test")
    root.geometry("600x400")
    root.configure(bg='#f0f0f0')
    
    # Title
    title = tk.Label(root, text="Debug PKI Dashboard - Button Test", 
                    font=('Arial', 16, 'bold'), bg='#f0f0f0')
    title.pack(pady=20)
    
    # Instructions
    instructions = tk.Label(root, 
                           text="Click each button to test if the applications open correctly:",
                           font=('Arial', 12), bg='#f0f0f0')
    instructions.pack(pady=10)
    
    # Button frame
    button_frame = tk.Frame(root, bg='#f0f0f0')
    button_frame.pack(expand=True, fill='both', padx=50, pady=20)
    
    # Test buttons
    buttons = [
        ("Test Registration", test_registration, '#3498db'),
        ("Test Login", test_login, '#f39c12'),
        ("Test Document Signer", test_signer, '#27ae60'),
        ("Test Document Verifier", test_verifier, '#9b59b6'),
        ("Test Use Case", test_usecase, '#34495e')
    ]
    
    for i, (text, command, color) in enumerate(buttons):
        btn = tk.Button(button_frame, text=text,
                       command=command,
                       bg=color, fg='white',
                       font=('Arial', 12, 'bold'),
                       padx=20, pady=10,
                       width=20)
        btn.pack(pady=10)
    
    # Status label
    status = tk.Label(root, text="Click buttons above to test functionality", 
                     font=('Arial', 10), bg='#f0f0f0', fg='#666')
    status.pack(pady=10)
    
    return root


def main():
    """Main function"""
    print("Starting Debug PKI Dashboard...")
    root = create_debug_dashboard()
    root.mainloop()


if __name__ == "__main__":
    main()
